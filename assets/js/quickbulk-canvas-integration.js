/**
 * SLMM QuickBulk Canvas Integration
 * Adds lightning bolt widgets to every D3.js card for instant bulk page creation
 * 
 * @version 1.0.0
 * <AUTHOR> SEO Bundle
 */

(function($, d3) {
    'use strict';

    // Check if debug mode is enabled
    var isDebugMode = (typeof slmmDirectEditorData !== 'undefined' && slmmDirectEditorData.debug_mode) ||
                     (typeof slmmLinkCheck !== 'undefined' && slmmLinkCheck.debug_mode) ||
                     window.slmmDebugMode ||
                     (new URLSearchParams(window.location.search).get('slmm_debug') === 'true');

    function debugLog() {
        if (isDebugMode && console && console.log) {
            console.log.apply(console, arguments);
        }
    }

    // Debug system integration
    const debug = {
        log: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.log(category, message, data);
            }
        },
        success: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.success(category, message, data);
            }
        },
        warn: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.warn(category, message, data);
            }
        },
        error: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.error(category, message, data);
            }
        }
    };
    
    // Ensure dependencies are available
    if (typeof $ === 'undefined' || typeof d3 === 'undefined') {
        debug.error('QuickBulk', 'Missing dependencies (jQuery or D3.js)');
        return;
    }
    
    /**
     * Main QuickBulk Controller Class
     */
    class SLMM_QuickBulk_Controller {
        
        constructor() {
            this.activePopups = new Map();
            this.d3TreeInstance = null;
            this.initialized = false;
            this.keyboardHandler = null;
            
            // Configuration
            this.config = {
                widgetSize: 24,
                popupWidth: 600,
                popupHeight: 450,
                maxPageTitles: 100,
                animationDuration: 200,
                debounceDelay: 300
            };
            
            this.init();
        }
        
        init() {
            if (this.initialized) return;
            
            // Wait for D3.js tree to be ready
            this.waitForTreeInstance(() => {
                this.enhanceAllCards();
                this.initKeyboardHandlers();
                this.bindGlobalEvents();
                this.initialized = true;
                
                if (typeof SlmmDebugLogger !== 'undefined') {
                    SlmmDebugLogger.log('Canvas integration initialized', null, 'quickbulk');
                }
            });
        }
        
        waitForTreeInstance(callback) {
            // Check for existing D3.js tree instance or interlinking suite context
            const checkInterval = setInterval(() => {
                const treeExists = window.slmmInterlinkingTree || 
                                  document.querySelector('.slmm-tree-svg') ||
                                  document.querySelector('#slmm-d3-container') ||
                                  document.querySelector('.slmm-d3-tree-container') ||
                                  document.querySelector('svg#slmm-tree-svg');
                
                if (treeExists) {
                    clearInterval(checkInterval);
                    this.d3TreeInstance = window.slmmInterlinkingTree || true;
                    callback();
                }
            }, 100);
            
            // Timeout after 10 seconds
            setTimeout(() => {
                clearInterval(checkInterval);
                if (!this.d3TreeInstance) {
                    debug.warn('QuickBulk', 'D3.js tree instance not found');
                    callback(); // Continue anyway
                }
            }, 10000);
        }
        
        enhanceAllCards() {
            // Enhance existing cards
            this.enhanceExistingCards();
            
            // Watch for new cards being added to the tree
            this.observeTreeUpdates();
        }
        
        enhanceExistingCards() {
            // Look for regular DOM tree nodes first
            const treeNodes = document.querySelectorAll('.slmm-tree-node, .tree-node, .node');
            
            treeNodes.forEach(node => {
                if (!node.querySelector('.slmm-quickbulk-trigger')) {
                    this.addQuickBulkWidget(node);
                }
            });
            
            // Also look for D3.js SVG nodes in interlinking suite context
            const d3TreeNodes = document.querySelectorAll('g[data-page-id], g.slmm-tree-node, svg g[data-post-type]');
            
            d3TreeNodes.forEach(node => {
                if (!node.querySelector('.slmm-quickbulk-widget, .slmm-quickbulk-trigger')) {
                    // For D3.js SVG nodes, we don't add HTML widgets since they're added by D3.js code
                    // This method mainly serves as a detection/counting mechanism
                }
            });
            
            const totalNodes = treeNodes.length + d3TreeNodes.length;
            if (typeof SlmmDebugLogger !== 'undefined') {
                SlmmDebugLogger.log('Enhanced existing cards', {treeNodeCount: treeNodes.length}, 'quickbulk');
            }
            
            // Additional D3.js context detection
            if (d3TreeNodes.length > 0 && treeNodes.length === 0) {
                if (typeof SlmmDebugLogger !== 'undefined') {
                    SlmmDebugLogger.log('Detected D3.js tree nodes - widgets handled by D3.js integration', {d3NodeCount: d3TreeNodes.length}, 'quickbulk');
                }
            }
        }
        
        observeTreeUpdates() {
            const treeContainer = document.querySelector(
                '.slmm-tree-container, .tree-container, .interlinking-canvas, ' +
                '.slmm-canvas-container, .slmm-d3-tree-container, .slmm-d3-container, #slmm-d3-container'
            );
            
            if (!treeContainer) {
                debug.warn('QuickBulk', 'Tree container not found for observation');
                return;
            }
            
            const observer = new MutationObserver((mutations) => {
                mutations.forEach(mutation => {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            const treeNodes = node.classList?.contains('slmm-tree-node') ? 
                                [node] : 
                                node.querySelectorAll?.('.slmm-tree-node, .tree-node, .node') || [];
                            
                            treeNodes.forEach(treeNode => {
                                if (!treeNode.querySelector('.slmm-quickbulk-trigger')) {
                                    this.addQuickBulkWidget(treeNode);
                                }
                            });
                        }
                    });
                });
            });
            
            observer.observe(treeContainer, {
                childList: true,
                subtree: true
            });
        }
        
        addQuickBulkWidget(cardElement) {
            // Extract card data
            const cardData = this.extractCardData(cardElement);
            if (!cardData) {
                debug.warn('QuickBulk', 'Could not extract card data', cardElement);
                return;
            }
            
            // Create widget container
            const widgetContainer = document.createElement('div');
            widgetContainer.className = 'slmm-quickbulk-widget';
            widgetContainer.style.cssText = `
                position: absolute;
                bottom: -8px;
                right: -8px;
                z-index: 10;
            `;
            
            // Create trigger button
            const triggerButton = document.createElement('button');
            triggerButton.className = 'slmm-quickbulk-trigger';
            triggerButton.setAttribute('aria-label', `Create child pages for ${cardData.title}`);
            triggerButton.setAttribute('title', `Bulk create pages under "${cardData.title}"`);
            triggerButton.innerHTML = '⚡';
            
            // Apply styles
            this.applyTriggerStyles(triggerButton);
            
            // Bind click event
            triggerButton.addEventListener('click', (event) => {
                event.preventDefault();
                event.stopPropagation();
                this.triggerQuickBulk(cardData, triggerButton);
            });
            
            // Add widget to card
            widgetContainer.appendChild(triggerButton);
            cardElement.style.position = 'relative'; // Ensure proper positioning
            cardElement.appendChild(widgetContainer);
            
            // Add hover effects to parent card
            this.addCardHoverEffects(cardElement, widgetContainer);
        }
        
        extractCardData(cardElement) {
            // Try various methods to extract card data
            let cardData = {
                element: cardElement,
                id: null,
                title: 'Unknown Page',
                hierarchyLevel: 1,
                contextKeywords: []
            };
            
            // Method 1: Check for data attributes
            if (cardElement.dataset.pageId) {
                cardData.id = cardElement.dataset.pageId;
            } else if (cardElement.dataset.nodeId) {
                cardData.id = cardElement.dataset.nodeId;
            }
            
            // Method 2: Check for D3.js bound data
            if (cardElement.__data__) {
                const d3Data = cardElement.__data__;
                cardData.id = d3Data.id || cardData.id;
                cardData.title = d3Data.title || d3Data.name || cardData.title;
                cardData.hierarchyLevel = d3Data.level || d3Data.depth || 1;

                // CRITICAL FIX: Handle category nodes - they should create posts, not category_nodes
                if (d3Data.data && d3Data.data.post_type) {
                    if (d3Data.data.post_type === 'category_node') {
                        // Category nodes should trigger post creation
                        cardData.post_type = 'post';
                    } else {
                        cardData.post_type = d3Data.data.post_type;
                    }
                }
            }
            
            // Method 3: Extract from DOM content
            const titleElement = cardElement.querySelector('.node-title, .card-title, .title, h3, h4');
            if (titleElement) {
                cardData.title = titleElement.textContent.trim() || cardData.title;
            }
            
            // Extract keywords from title
            cardData.contextKeywords = this.extractKeywords(cardData.title);
            
            // Generate ID if missing
            if (!cardData.id) {
                cardData.id = this.generateCardId(cardData.title);
            }
            
            return cardData;
        }
        
        extractKeywords(title) {
            // Simple keyword extraction
            return title
                .toLowerCase()
                .replace(/[^\w\s]/g, ' ')
                .split(/\s+/)
                .filter(word => word.length > 3)
                .slice(0, 5);
        }
        
        generateCardId(title) {
            return 'card_' + title
                .toLowerCase()
                .replace(/[^\w]/g, '_')
                .substring(0, 20) + 
                '_' + Date.now();
        }
        
        applyTriggerStyles(triggerButton) {
            triggerButton.style.cssText = `
                width: ${this.config.widgetSize}px;
                height: ${this.config.widgetSize}px;
                background: linear-gradient(135deg, #3b82f6, #1d4ed8);
                border: 2px solid white;
                border-radius: 50%;
                cursor: pointer;
                opacity: 0;
                transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                color: white;
                line-height: 1;
                padding: 0;
                transform: scale(0.9);
            `;
            
            // Add hover effects using CSS classes to avoid forced reflows
            triggerButton.addEventListener('mouseenter', () => {
                triggerButton.classList.add('hovered');
            });
            
            triggerButton.addEventListener('mouseleave', () => {
                triggerButton.classList.remove('hovered');
            });
        }
        
        addCardHoverEffects(cardElement, widgetContainer) {
            const trigger = widgetContainer.querySelector('.slmm-quickbulk-trigger');
            
            cardElement.addEventListener('mouseenter', () => {
                trigger.classList.add('card-hovered');
            });
            
            cardElement.addEventListener('mouseleave', () => {
                if (!trigger.classList.contains('active')) {
                    trigger.classList.remove('card-hovered');
                }
            });
        }
        
        triggerQuickBulk(cardData, triggerElement) {
            debug.log('QuickBulk', 'triggerQuickBulk called for:', cardData.id, cardData.title);
            
            // Enhanced state validation and cleanup
            try {
                // Check if popup already exists for this card
                const existingPopup = this.activePopups.get(cardData.id);
                if (existingPopup) {
                    debug.log('QuickBulk', 'Existing popup found, cleaning up first');
                    existingPopup.destroy();
                    this.activePopups.delete(cardData.id);
                }
                
                // Close any other open popups with enhanced cleanup
                this.closeAllPopups();
                
                // Split the work into immediate feedback and deferred heavy operations
                // Immediate: Update UI feedback
                if (triggerElement && cardData?.id && cardData?.title) {
                    triggerElement.classList.add('active');
                }
                
                // Deferred: Heavy DOM operations in next frame
                requestAnimationFrame(() => {
                    try {
                        // Validate cardData before proceeding
                        if (!cardData || !cardData.id || !cardData.title) {
                            debug.error('QuickBulk', 'Invalid cardData:', cardData);
                            return;
                        }
                        
                        // Validate trigger element
                        if (!triggerElement || !triggerElement.style) {
                            debug.error('QuickBulk', 'Invalid triggerElement:', triggerElement);
                            return;
                        }
                        
                        // Create popup instance (lightweight constructor)
                        debug.log('QuickBulk', 'Creating new popup instance');
                        const popup = new SLMM_QuickBulk_Popup(cardData, triggerElement, this);
                        
                        if (!popup) {
                            debug.error('QuickBulk', 'Failed to create popup instance');
                            this.resetTriggerState(triggerElement);
                            return;
                        }
                        
                        // Store popup reference BEFORE showing to prevent race conditions
                        this.activePopups.set(cardData.id, popup);
                        debug.log('QuickBulk', 'Popup stored in activePopups, total active:', this.activePopups.size);
                        
                        // Defer heavy DOM creation to next frame
                        requestAnimationFrame(() => {
                            popup.show();
                        });
                        
                        // Verify popup was created successfully
                        if (!popup.isVisible) {
                            debug.warn('QuickBulk', 'Popup show() failed, cleaning up');
                            this.activePopups.delete(cardData.id);
                            this.resetTriggerState(triggerElement);
                        }
                        
                    } catch (creationError) {
                        debug.error('QuickBulk', 'Error during popup creation:', creationError);
                        this.resetTriggerState(triggerElement);
                        this.activePopups.delete(cardData.id);
                    }
                }, 50); // Small delay to ensure cleanup is complete
                
            } catch (error) {
                debug.error('QuickBulk', 'Error in triggerQuickBulk:', error);
                this.resetTriggerState(triggerElement);
            }
        }
        
        resetTriggerState(triggerElement) {
            if (triggerElement) {
                triggerElement.classList.remove('active', 'hovered', 'card-hovered');
            }
        }
        
        showPopup(parentData, x = null, y = null) {
            try {
                // Validate input data
                if (!parentData || typeof parentData !== 'object') {
                    debug.error('QuickBulk', 'Invalid parentData provided to showPopup:', parentData);
                    return null;
                }
                
                // Convert D3.js parentData to expected cardData format with validation
                const cardData = {
                    id: parentData.id || this.generateCardId(parentData.title || parentData.name || 'untitled'),
                    title: parentData.title || parentData.name || 'Unnamed Page',
                    post_type: parentData.target_post_type || parentData.post_type || 'page', // CRITICAL FIX: Use target type first
                    element: null // D3.js context doesn't have DOM element
                };
                
                // Validate required cardData fields
                if (!cardData.id || !cardData.title) {
                    debug.error('QuickBulk', 'Invalid card data generated:', cardData);
                    return null;
                }
                
                // Check if popup already exists for this card
                if (this.activePopups.has(cardData.id)) {
                    debug.warn('QuickBulk', 'Popup already exists for card ID:', cardData.id);
                    return cardData.id; // Return existing popup ID
                }
                
                // Create mock trigger element for popup positioning
                const mockTrigger = document.createElement('div');
                mockTrigger.className = 'slmm-quickbulk-trigger mock-trigger';
                mockTrigger.style.position = 'fixed';
                mockTrigger.style.zIndex = '100000';
                mockTrigger.style.pointerEvents = 'none';
                mockTrigger.style.visibility = 'hidden';
                mockTrigger.style.width = '24px';
                mockTrigger.style.height = '24px';
                
                // Validate and position the mock trigger at provided coordinates
                if (x !== null && y !== null && !isNaN(x) && !isNaN(y)) {
                    // Ensure coordinates are within viewport bounds
                    const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
                    const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
                    
                    const safeX = Math.max(0, Math.min(x, viewportWidth - 350)); // Account for popup width
                    const safeY = Math.max(0, Math.min(y, viewportHeight - 500)); // Account for popup height
                    
                    mockTrigger.style.left = safeX + 'px';
                    mockTrigger.style.top = safeY + 'px';
                } else {
                    // Fallback to center screen
                    mockTrigger.style.left = '50vw';
                    mockTrigger.style.top = '50vh';
                    mockTrigger.style.transform = 'translate(-50%, -50%)';
                }
                
                if (!document.body) {
                    debug.error('QuickBulk', 'Document body not available');
                    return null;
                }
                
                document.body.appendChild(mockTrigger);
                
                // Use existing triggerQuickBulk method with error handling
                try {
                    this.triggerQuickBulk(cardData, mockTrigger);
                } catch (triggerError) {
                    debug.error('QuickBulk', 'Error triggering popup:', triggerError);
                    // Clean up mock trigger on error
                    if (mockTrigger && mockTrigger.parentNode) {
                        mockTrigger.remove();
                    }
                    return null;
                }
                
                // Clean up mock element after popup is positioned (but keep it for popup lifecycle)
                const popup = this.activePopups.get(cardData.id);
                if (popup && popup.element) {
                    // When popup closes, clean up the mock trigger
                    const originalDestroy = popup.destroy.bind(popup);
                    popup.destroy = function() {
                        try {
                            if (mockTrigger && mockTrigger.parentNode) {
                                mockTrigger.remove();
                            }
                        } catch (cleanupError) {
                            debug.warn('QuickBulk', 'Error cleaning up mock trigger:', cleanupError);
                        }
                        originalDestroy();
                    };
                } else {
                    // Fallback cleanup if popup creation failed
                    setTimeout(() => {
                        try {
                            if (mockTrigger && mockTrigger.parentNode) {
                                mockTrigger.remove();
                            }
                        } catch (cleanupError) {
                            debug.warn('QuickBulk', 'Error in fallback cleanup:', cleanupError);
                        }
                    }, 1000);
                }
                
                return cardData.id; // Return the popup ID for tracking
                
            } catch (error) {
                debug.error('QuickBulk', 'Unexpected error in showPopup:', error);
                return null;
            }
        }
        
        closePopup(cardId) {
            debug.log('QuickBulk', 'closePopup called for:', cardId);
            
            try {
                const popup = this.activePopups.get(cardId);
                if (popup) {
                    debug.log('QuickBulk', 'Found popup to close, destroying...');
                    
                    // Destroy popup (this will also remove itself from activePopups)
                    popup.destroy();
                    
                    // Double-check removal from activePopups
                    if (this.activePopups.has(cardId)) {
                        this.activePopups.delete(cardId);
                        debug.log('QuickBulk', 'Force-removed popup from activePopups');
                    }
                } else {
                    debug.log('QuickBulk', 'No popup found for cardId:', cardId);
                }
                
                debug.log('QuickBulk', 'closePopup complete, active popups:', this.activePopups.size);
                
            } catch (error) {
                debug.error('QuickBulk', 'Error in closePopup:', error);
                // Force removal on error
                this.activePopups.delete(cardId);
            }
        }
        
        closeAllPopups() {
            debug.log('QuickBulk', 'closeAllPopups called, active popups:', this.activePopups.size);
            
            try {
                // Create a copy of the entries to avoid iteration issues
                const popupsToClose = Array.from(this.activePopups.entries());
                
                popupsToClose.forEach(([cardId, popup]) => {
                    try {
                        debug.log('QuickBulk', 'Closing popup:', cardId);
                        if (popup && typeof popup.destroy === 'function') {
                            popup.destroy();
                        }
                    } catch (popupError) {
                        debug.warn('QuickBulk', 'Error destroying popup:', cardId, popupError);
                    }
                });
                
                // Clear the entire Map
                this.activePopups.clear();
                debug.log('QuickBulk', 'All popups closed, activePopups cleared');
                
            } catch (error) {
                debug.error('QuickBulk', 'Error in closeAllPopups:', error);
                // Force clear on error
                this.activePopups.clear();
            }
        }
        
        initKeyboardHandlers() {
            this.keyboardHandler = new SLMM_QuickBulk_KeyboardHandler(this);
        }
        
        bindGlobalEvents() {
            // Close popups on outside click
            document.addEventListener('click', (event) => {
                if (!event.target.closest('.slmm-quickbulk-popup') && 
                    !event.target.closest('.slmm-quickbulk-trigger')) {
                    this.closeAllPopups();
                }
            });
            
            // Handle escape key globally
            document.addEventListener('keydown', (event) => {
                if (event.key === 'Escape') {
                    this.closeAllPopups();
                }
            });
        }
        
        // API for external integration
        enhanceCard(cardElement) {
            if (cardElement && !cardElement.querySelector('.slmm-quickbulk-trigger')) {
                this.addQuickBulkWidget(cardElement);
            }
        }
        
        getActivePopup(cardId) {
            return this.activePopups.get(cardId);
        }
        
        getAllActivePopups() {
            return Array.from(this.activePopups.values());
        }
        
        // Comprehensive validation system for popup creation
        validateSystem() {
            debug.log('QuickBulk Validation', 'SLMM QuickBulk: Starting system validation...');
            
            const validationResults = {
                controllerReady: false,
                widgetsFound: 0,
                popupCreationTest: false,
                cleanupTest: false,
                errorHandlingTest: false,
                overall: false
            };
            
            try {
                // Test 1: Controller readiness
                validationResults.controllerReady = typeof this.activePopups !== 'undefined' && this.activePopups instanceof Map;
                if (validationResults.controllerReady) {
                    debug.success('QuickBulk Validation', 'Controller readiness: PASS');
                } else {
                    debug.error('QuickBulk Validation', 'Controller readiness: FAIL');
                }
                
                // Test 2: Widget availability
                const widgets = document.querySelectorAll('.slmm-quickbulk-widget');
                validationResults.widgetsFound = widgets.length;
                if (validationResults.widgetsFound > 0) {
                    debug.success('QuickBulk Validation', `Widgets found: ${validationResults.widgetsFound} PASS`);
                } else {
                    debug.error('QuickBulk Validation', `Widgets found: ${validationResults.widgetsFound} FAIL`);
                }
                
                // Test 3: Popup creation test (using mock data)
                const testCardData = {
                    id: 'validation-test-' + Date.now(),
                    title: 'Validation Test Page',
                    post_type: 'page'
                };
                
                // Create mock trigger element for testing
                const mockTrigger = document.createElement('div');
                mockTrigger.style.cssText = 'position: fixed; top: -100px; left: -100px; visibility: hidden;';
                document.body.appendChild(mockTrigger);
                
                try {
                    this.triggerQuickBulk(testCardData, mockTrigger);
                    
                    // Check if popup was created
                    setTimeout(() => {
                        const testPopup = this.activePopups.get(testCardData.id);
                        if (testPopup) {
                            validationResults.popupCreationTest = true;
                            debug.success('QuickBulk Validation', 'Popup creation test: PASS');
                            
                            // Test 4: Cleanup test
                            try {
                                testPopup.destroy();
                                const cleanupCheck = !this.activePopups.has(testCardData.id);
                                validationResults.cleanupTest = cleanupCheck;
                                if (cleanupCheck) {
                                    debug.success('QuickBulk Validation', 'Cleanup test: PASS');
                                } else {
                                    debug.error('QuickBulk Validation', 'Cleanup test: FAIL');
                                }
                            } catch (cleanupError) {
                                debug.error('QuickBulk Validation', 'Cleanup test failed:', cleanupError);
                                validationResults.cleanupTest = false;
                            }
                        } else {
                            debug.error('QuickBulk Validation', 'Popup creation test: FAIL - No popup created');
                            validationResults.popupCreationTest = false;
                        }
                        
                        // Test 5: Error handling test
                        try {
                            // Test with invalid data
                            this.triggerQuickBulk(null, null);
                            validationResults.errorHandlingTest = true; // Should not crash
                            debug.success('QuickBulk Validation', 'Error handling test: PASS');
                        } catch (errorHandlingError) {
                            debug.error('QuickBulk Validation', 'Error handling test: FAIL -', errorHandlingError.message);
                            validationResults.errorHandlingTest = false;
                        }
                        
                        // Overall validation
                        validationResults.overall = validationResults.controllerReady &&
                                                  validationResults.widgetsFound > 0 &&
                                                  validationResults.popupCreationTest &&
                                                  validationResults.cleanupTest &&
                                                  validationResults.errorHandlingTest;
                        
                        debug.log('QuickBulk Validation', 'SLMM QuickBulk Validation Results:', validationResults);
                        if (validationResults.overall) {
                            debug.success('QuickBulk Validation', 'Overall system health: EXCELLENT');
                        } else {
                            debug.error('QuickBulk Validation', 'Overall system health: NEEDS ATTENTION');
                        }
                        
                        // Cleanup mock trigger
                        mockTrigger.remove();
                        
                        return validationResults;
                        
                    }, 1000);
                    
                } catch (testError) {
                    debug.error('QuickBulk Validation', 'Popup creation test failed:', testError);
                    validationResults.popupCreationTest = false;
                    mockTrigger.remove();
                }
                
            } catch (validationError) {
                debug.error('QuickBulk Validation', 'System validation failed:', validationError);
                validationResults.overall = false;
            }
            
            return validationResults;
        }
        
        // Health check function for periodic monitoring
        healthCheck() {
            const health = {
                timestamp: new Date().toISOString(),
                activePopups: this.activePopups.size,
                widgetCount: document.querySelectorAll('.slmm-quickbulk-widget').length,
                nodeCount: document.querySelectorAll('.slmm-tree-node, .tree-node, .node').length,
                controllerStatus: 'ready',
                issues: []
            };
            
            // Check for orphaned popups
            if (health.activePopups > 0) {
                this.activePopups.forEach((popup, cardId) => {
                    if (!popup.isVisible || !popup.element) {
                        health.issues.push(`Orphaned popup detected: ${cardId}`);
                    }
                });
            }
            
            // Check for missing widgets
            const nodesWithoutWidgets = document.querySelectorAll('.slmm-tree-node:not(.slmm-quickbulk-enabled), .tree-node:not(.slmm-quickbulk-enabled), .node:not(.slmm-quickbulk-enabled)');
            if (nodesWithoutWidgets.length > 0) {
                health.issues.push(`${nodesWithoutWidgets.length} nodes missing QuickBulk widgets`);
            }
            
            health.status = health.issues.length === 0 ? 'healthy' : 'needs-attention';
            
            debug.log('QuickBulk Health', 'SLMM QuickBulk Health Check:', health);
            return health;
        }
    }
    
    /**
     * QuickBulk Popup Class
     */
    class SLMM_QuickBulk_Popup {
        
        constructor(cardData, triggerElement, controller) {
            this.cardData = cardData;
            this.triggerElement = triggerElement;
            this.controller = controller;
            this.element = null;
            this.isVisible = false;
            
            // State
            this.currentInput = '';
            this.currentSlugInput = '';
            this.parsedPages = [];
            this.isCreating = false;
            
            // Debounced methods
            this.debouncedUpdatePreview = this.debounce(this.updatePreview.bind(this), 300);
            this.debouncedDetectClipboard = this.debounce(this.detectClipboardContent.bind(this), 500);
        }
        
        show() {
            if (this.isVisible) return;
            
            // Critical path: Create and position immediately
            this.createElement();
            this.positionPopup();
            this.isVisible = true;
            
            // Defer non-critical operations
            requestAnimationFrame(() => {
                this.bindEvents();
                this.animateIn();
                
                // Further defer optional operations
                setTimeout(() => {
                    this.focusInput();
                    this.debouncedDetectClipboard();
                }, 50);
            });
        }
        
        createElement() {
            this.element = document.createElement('div');
            this.element.className = 'slmm-quickbulk-popup';
            this.element.setAttribute('role', 'dialog');
            this.element.setAttribute('aria-labelledby', 'quickbulk-title');
            
            // Create minimal structure immediately for positioning
            this.element.innerHTML = `<div class="popup-header">Loading...</div>`;
            document.body.appendChild(this.element);
            
            // Defer full content creation
            requestAnimationFrame(() => {
                this.element.innerHTML = this.getPopupHTML();
            });
        }
        
        getPopupHTML() {
            return `
                <div class="popup-header">
                    <div class="parent-context">
                        <span class="context-icon">📄</span>
                        <span class="context-text">Adding to: <strong>${this.escapeHtml(this.cardData.title)}</strong></span>
                    </div>
                    <button class="close-btn" aria-label="Close" title="Close (Esc)">×</button>
                </div>
                
                <div class="popup-body">
                    <div class="input-section">
                        <div class="dual-input-container">
                            <div class="input-column titles-column">
                                <div class="input-header">
                                    <label for="quickbulk-textarea" id="quickbulk-title">Page Titles (one per line)</label>
                                    <div class="input-actions">
                                        <button id="paste-btn" class="quick-action" title="Paste from clipboard (Ctrl+V)">📋</button>
                                        <button id="ai-suggest-btn" class="quick-action" title="AI suggestions">🤖</button>
                                        <button id="clear-btn" class="quick-action" title="Clear all">🗑️</button>
                                    </div>
                                </div>
                                
                                <textarea 
                                    id="quickbulk-textarea" 
                                    class="quickbulk-input"
                                    placeholder="Paste or type page titles here...

Example:
• ${this.cardData.title} for Beginners
• Advanced ${this.cardData.title} Guide  
• ${this.cardData.title} Best Practices
• Common ${this.cardData.title} Mistakes"
                                    rows="6"
                                    maxlength="5000"></textarea>
                            </div>
                            
                            <div class="input-column slugs-column">
                                <div class="input-header slug-input-header">
                                    <label for="quickbulk-slug-textarea" id="quickbulk-slug-title">Page Slugs (one per line, optional)</label>
                                    <div class="input-actions">
                                        <button id="paste-slug-btn" class="quick-action" title="Paste slugs from clipboard">📋</button>
                                        <button id="generate-slug-btn" class="quick-action" title="Auto-generate slugs from titles">🔄</button>
                                        <button id="clear-slug-btn" class="quick-action" title="Clear all slugs">🗑️</button>
                                    </div>
                                </div>
                                
                                <textarea 
                                    id="quickbulk-slug-textarea" 
                                    class="quickbulk-input slug-input"
                                    placeholder="Optional: Custom slugs for each page...

Example:
• ${this.generateSlug(this.cardData.title)}-beginners
• advanced-${this.generateSlug(this.cardData.title)}-guide  
• ${this.generateSlug(this.cardData.title)}-best-practices
• common-${this.generateSlug(this.cardData.title)}-mistakes

Leave empty to auto-generate from titles"
                                    rows="6"
                                    maxlength="5000"></textarea>
                            </div>
                        </div>
                        
                        <div id="clipboard-suggestion" class="clipboard-suggestion" style="display: none;">
                            <div class="suggestion-header">
                                📋 Clipboard content detected
                                <button class="use-clipboard-btn">Use This</button>
                            </div>
                            <div class="suggestion-preview"></div>
                        </div>
                    </div>
                    
                    <div class="preview-section">
                        <div class="preview-header">
                            <span class="preview-title">Preview</span>
                            <span class="page-counter">Pages: <strong id="page-count">0</strong></span>
                        </div>
                        
                        <div id="preview-list" class="preview-list">
                            <div class="empty-state">Enter page titles to see preview</div>
                        </div>
                    </div>
                    
                    <div id="ai-suggestions" class="ai-suggestions" style="display: none;">
                        <div class="suggestions-header">
                            🤖 AI Suggestions for "${this.escapeHtml(this.cardData.title)}"
                            <button class="collapse-suggestions" title="Hide suggestions">−</button>
                        </div>
                        <div class="suggestions-grid" id="suggestions-grid">
                            <div class="suggestions-loading">Generating ideas...</div>
                        </div>
                    </div>
                </div>
                
                <div class="popup-footer">
                    <div class="creation-options">
                        <label class="option">
                            <input type="checkbox" id="auto-link" checked>
                            <span>Auto-link to parent</span>
                        </label>
                        <select id="page-status" class="status-select">
                            <option value="draft">Draft</option>
                            <option value="publish" selected>Published</option>
                            <option value="private">Private</option>
                        </select>
                    </div>
                    ${this.getCategorySelectionHTML()}
                    
                    <div class="action-buttons">
                        <button id="create-pages-btn" class="create-btn" disabled>
                            ⚡ Create <span id="create-count">0</span> Pages
                        </button>
                    </div>
                </div>
                
                <div id="creation-progress" class="progress-overlay" style="display: none;">
                    <div class="progress-content">
                        <div class="progress-header">Creating Pages...</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progress-fill"></div>
                        </div>
                        <div class="progress-details" id="progress-details">
                            Preparing pages for creation...
                        </div>
                        <div class="progress-stats">
                            <span id="progress-current">0</span> / <span id="progress-total">0</span>
                        </div>
                    </div>
                </div>
            `;
        }
        
        positionPopup() {
            // Use viewport center as fallback to avoid forced reflow from getBoundingClientRect
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            const popupWidth = this.controller.config.popupWidth;
            const popupHeight = this.controller.config.popupHeight;
            
            // Default to center screen to avoid getBoundingClientRect reflow
            let x = (viewportWidth - popupWidth) / 2;
            let y = (viewportHeight - popupHeight) / 2;
            
            // Only set position - all other styles are in CSS
            this.element.style.left = x + 'px';
            this.element.style.top = y + 'px';
            this.element.classList.add('positioned');
        }
        
        animateIn() {
            requestAnimationFrame(() => {
                this.element.style.opacity = '1';
                this.element.style.transform = 'scale(1) translateY(0)';
            });
        }
        
        bindEvents() {
            const textarea = this.element.querySelector('#quickbulk-textarea');
            const slugTextarea = this.element.querySelector('#quickbulk-slug-textarea');
            const closeBtn = this.element.querySelector('.close-btn');
            const pasteBtn = this.element.querySelector('#paste-btn');
            const pasteSlugBtn = this.element.querySelector('#paste-slug-btn');
            const aiSuggestBtn = this.element.querySelector('#ai-suggest-btn');
            const clearBtn = this.element.querySelector('#clear-btn');
            const generateSlugBtn = this.element.querySelector('#generate-slug-btn');
            const clearSlugBtn = this.element.querySelector('#clear-slug-btn');
            const createBtn = this.element.querySelector('#create-pages-btn');
            
            // Input events
            textarea.addEventListener('input', () => {
                this.currentInput = textarea.value;
                this.debouncedUpdatePreview();
            });
            
            // Slug textarea events with real-time space-to-dash conversion
            slugTextarea.addEventListener('input', () => {
                const cursorPosition = slugTextarea.selectionStart;
                const rawValue = slugTextarea.value;
                
                // Apply real-time formatting: lowercase + spaces to dashes
                const formattedValue = this.formatSlugInput(rawValue);
                
                // Update the textarea if formatting changed anything
                if (formattedValue !== rawValue) {
                    slugTextarea.value = formattedValue;
                    // Restore cursor position
                    slugTextarea.setSelectionRange(cursorPosition, cursorPosition);
                }
                
                this.currentSlugInput = slugTextarea.value;
                this.debouncedUpdatePreview();
            });
            
            textarea.addEventListener('paste', (event) => {
                setTimeout(() => {
                    this.currentInput = textarea.value;
                    this.debouncedUpdatePreview();
                }, 10);
            });
            
            // Button events
            closeBtn.addEventListener('click', () => this.close());
            pasteBtn.addEventListener('click', () => this.handlePaste());
            pasteSlugBtn.addEventListener('click', () => this.handleSlugPaste());
            aiSuggestBtn.addEventListener('click', () => this.showAISuggestions());
            clearBtn.addEventListener('click', () => this.clearInput());
            generateSlugBtn.addEventListener('click', () => this.generateSlugsFromTitles());
            clearSlugBtn.addEventListener('click', () => this.clearSlugs());
            createBtn.addEventListener('click', () => this.createPages());
            
            // Slug edit delegation
            this.element.addEventListener('click', (event) => {
                if (event.target.classList.contains('slug-value') && event.target.classList.contains('editable')) {
                    const index = parseInt(event.target.dataset.slugIndex);
                    this.editSlug(index);
                }
            });
            
            // Keyboard shortcuts
            this.element.addEventListener('keydown', (event) => {
                if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
                    event.preventDefault();
                    this.createPages();
                } else if (event.key === 'Escape') {
                    event.preventDefault();
                    this.close();
                }
            });
            
            // Dragging functionality
            this.initializeDragging();
        }
        
        initializeDragging() {
            const header = this.element.querySelector('.popup-header');
            if (!header) return;
            
            let isDragging = false;
            let dragOffset = { x: 0, y: 0 };
            
            // Add visual indicator that header is draggable
            header.style.cursor = 'move';
            header.title = 'Drag to reposition popup';
            
            const startDrag = (e) => {
                // Prevent dragging when clicking on close button or other controls
                if (e.target.closest('.close-btn') || e.target.closest('button')) {
                    return;
                }
                
                isDragging = true;
                const rect = this.element.getBoundingClientRect();
                dragOffset.x = e.clientX - rect.left;
                dragOffset.y = e.clientY - rect.top;
                
                // Add dragging class for visual feedback
                this.element.classList.add('dragging');
                
                // Prevent text selection during drag
                e.preventDefault();
                
                // Add global listeners for smooth dragging
                document.addEventListener('mousemove', handleDrag);
                document.addEventListener('mouseup', stopDrag);
            };
            
            const handleDrag = (e) => {
                if (!isDragging) return;
                
                // Calculate new position
                let newX = e.clientX - dragOffset.x;
                let newY = e.clientY - dragOffset.y;
                
                // Keep popup within viewport bounds
                const popupRect = this.element.getBoundingClientRect();
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;
                
                // Constrain to viewport with small margin
                const margin = 20;
                newX = Math.max(margin, Math.min(newX, viewportWidth - popupRect.width - margin));
                newY = Math.max(margin, Math.min(newY, viewportHeight - popupRect.height - margin));
                
                // Apply position
                this.element.style.left = newX + 'px';
                this.element.style.top = newY + 'px';
            };
            
            const stopDrag = () => {
                if (!isDragging) return;
                
                isDragging = false;
                this.element.classList.remove('dragging');
                
                // Remove global listeners
                document.removeEventListener('mousemove', handleDrag);
                document.removeEventListener('mouseup', stopDrag);
            };
            
            // Bind drag events to header
            header.addEventListener('mousedown', startDrag);
            
            // Prevent context menu during dragging
            header.addEventListener('contextmenu', (e) => {
                if (isDragging) e.preventDefault();
            });
        }
        
        focusInput() {
            const textarea = this.element.querySelector('#quickbulk-textarea');
            setTimeout(() => {
                textarea.focus();
                textarea.setSelectionRange(0, 0);
            }, 250);
        }
        
        async detectClipboardContent() {
            try {
                if (navigator.clipboard && navigator.clipboard.readText) {
                    const clipboardText = await navigator.clipboard.readText();
                    if (clipboardText && this.looksLikePageTitles(clipboardText)) {
                        this.showClipboardSuggestion(clipboardText);
                    }
                }
            } catch (error) {
                // Clipboard access denied - that's fine
                debug.log('QuickBulk', 'Clipboard access not available');
            }
        }
        
        looksLikePageTitles(text) {
            const lines = text.split('\n').filter(line => line.trim());
            return lines.length >= 2 && 
                   lines.length <= 50 && 
                   lines.every(line => line.trim().length > 3 && line.trim().length < 200);
        }
        
        showClipboardSuggestion(text) {
            const suggestion = this.element.querySelector('#clipboard-suggestion');
            const preview = suggestion.querySelector('.suggestion-preview');
            const useBtn = suggestion.querySelector('.use-clipboard-btn');
            
            const lines = text.split('\n').filter(line => line.trim());
            const previewText = lines.slice(0, 3).join('\n') + 
                              (lines.length > 3 ? `\n... and ${lines.length - 3} more` : '');
            
            preview.textContent = previewText;
            suggestion.style.display = 'block';
            
            useBtn.onclick = () => {
                const textarea = this.element.querySelector('#quickbulk-textarea');
                textarea.value = text;
                this.currentInput = text;
                this.updatePreview();
                suggestion.style.display = 'none';
            };
        }
        
        async updatePreview() {
            const titleLines = this.currentInput.split('\n')
                .map(line => line.trim())
                .filter(line => line.length > 0);
            
            const slugLines = (this.currentSlugInput || '').split('\n')
                .map(line => line.trim());
                // DON'T filter out empty lines - they need to maintain index position
            
            // If no input, clear the preview
            if (titleLines.length === 0) {
                this.parsedPages = [];
                this.updatePreviewUI();
                return;
            }
            
            // Create page objects with both title and slug
            this.parsedPages = titleLines.slice(0, 100).map((title, index) => {
                const slug = slugLines[index] || this.generateSlug(title); // Auto-generate if slug not provided
                return {
                    title: title,
                    slug: slug,
                    input_type_detected: 'dual',
                    original_input: title,
                    fallback: false
                };
            });
            
            this.updatePreviewUI();
        }
        
        async validatePageTitlesWithBackend(pageText) {
            const formData = new FormData();
            formData.append('action', 'slmm_validate_page_titles');
            formData.append('nonce', slmmInterlinkingData?.nonce || '');
            formData.append('page_titles', pageText);
            
            const response = await fetch(ajaxurl, {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.data?.message || 'Backend validation failed');
            }
            
            return result.data;
        }
        
        updatePreviewUI() {
            // Update counter
            const pageCount = this.element.querySelector('#page-count');
            const createCount = this.element.querySelector('#create-count');
            const createBtn = this.element.querySelector('#create-pages-btn');
            
            pageCount.textContent = this.parsedPages.length;
            createCount.textContent = this.parsedPages.length;
            createBtn.disabled = this.parsedPages.length === 0;
            
            // Update preview list
            this.updatePreviewList();
        }
        
        updatePreviewList() {
            const previewList = this.element.querySelector('#preview-list');
            
            if (this.parsedPages.length === 0) {
                previewList.innerHTML = '<div class="empty-state">Enter page titles to see preview</div>';
                return;
            }
            
            const previewHTML = this.parsedPages.slice(0, 10).map((pageData, index) => {
                // Handle both old format (strings) and new format (objects)
                let title, slug;
                
                if (typeof pageData === 'string') {
                    // Legacy fallback for old format
                    title = pageData;
                    slug = this.generateSlug(pageData);
                } else {
                    // New format with intelligent processing
                    title = pageData.title || '';
                    slug = pageData.slug || this.generateSlug(title);
                }
                
                return `
                    <div class="preview-item valid">
                        <span class="preview-number">${index + 1}.</span>
                        <div class="preview-content">
                            <div class="preview-title">${this.escapeHtml(title)}</div>
                            <div class="preview-slug" data-index="${index}">
                                <span class="slug-label">URL:</span>
                                <span class="slug-value editable" data-slug-index="${index}" title="Click to edit">
                                    ${this.generatePreviewURL(slug)}
                                </span>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
            
            const moreHTML = this.parsedPages.length > 10 ? 
                `<div class="preview-more">...and ${this.parsedPages.length - 10} more</div>` : '';
            
            previewList.innerHTML = previewHTML + moreHTML;
        }
        
        /**
         * SIMPLE: Convert slug to title (replace dashes with spaces, title case)
         */
        convertSlugToTitle(slug) {
            // Step 1: Replace dashes with spaces
            let title = slug.replace(/-/g, ' ');
            
            // Step 2: Convert to title case (capitalize each word)
            title = title.split(' ')
                        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                        .join(' ');
            
            return title;
        }
        
        generateSlug(title) {
            return title
                .toLowerCase()
                .replace(/[^a-z0-9\s]/g, '')
                .replace(/\s+/g, '-')
                .substring(0, 50);
        }

        generatePreviewURL(slug) {
            // CRITICAL FIX: Generate correct preview URL based on post type and category structure

            if (this.cardData.post_type === 'post' && slmmQuickBulkData?.uses_category_permalinks) {
                // For category-based permalinks, show category/slug structure
                let categorySlug = 'uncategorized'; // Default

                // If we're creating under a category, use that category's slug
                if (this.cardData.id && this.cardData.id.startsWith('category_')) {
                    // Extract category slug from the category node title
                    categorySlug = this.cardData.title ? this.generateSlug(this.cardData.title) : 'category';
                }

                const previewURL = `/${categorySlug}/${this.escapeHtml(slug)}/`;
                return previewURL;
            } else {
                // For regular posts or pages, use simple slug structure
                const previewURL = `/${this.escapeHtml(slug)}/`;
                return previewURL;
            }
        }

        formatSlugInput(text) {
            if (!text) {
                return '';
            }
            
            return text
                .toLowerCase()                     // Convert to lowercase
                .replace(/[ \t]+/g, '-');         // Replace spaces and tabs with dashes (preserve newlines)
        }
        
        async handlePaste() {
            try {
                if (navigator.clipboard) {
                    const text = await navigator.clipboard.readText();
                    const textarea = this.element.querySelector('#quickbulk-textarea');
                    textarea.value = text;
                    this.currentInput = text;
                    this.updatePreview();
                }
            } catch (error) {
                // Show fallback message
                this.showPasteMessage();
            }
        }
        
        showPasteMessage() {
            const message = document.createElement('div');
            message.className = 'paste-message';
            message.innerHTML = '💡 Use Ctrl+V to paste content into the text area';
            message.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: #374151;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
                color: #f3f4f6;
                z-index: 100;
                animation: fadeIn 0.2s ease-in;
            `;
            
            this.element.appendChild(message);
            setTimeout(() => message.remove(), 3000);
        }
        
        showAISuggestions() {
            const suggestionsSection = this.element.querySelector('#ai-suggestions');
            const suggestionsGrid = this.element.querySelector('#suggestions-grid');
            
            suggestionsSection.style.display = 'block';
            suggestionsGrid.innerHTML = '<div class="suggestions-loading">Generating ideas...</div>';
            
            // Generate suggestions (fallback to templates for now)
            setTimeout(() => {
                const suggestions = this.generateSuggestions();
                this.displaySuggestions(suggestions);
            }, 1000);
        }
        
        generateSuggestions() {
            const title = this.cardData.title.toLowerCase();
            const templates = {
                'guide': [
                    `${this.cardData.title} for Beginners`,
                    `Advanced ${this.cardData.title}`,
                    `${this.cardData.title} Best Practices`,
                    `Common ${this.cardData.title} Mistakes`,
                    `${this.cardData.title} Tools & Resources`
                ],
                'seo': [
                    'Keyword Research Fundamentals',
                    'On-Page Optimization',
                    'Technical SEO Basics',
                    'Link Building Strategies',
                    'Content Optimization'
                ],
                'tutorial': [
                    `${this.cardData.title} Step-by-Step`,
                    `${this.cardData.title} Examples`,
                    `${this.cardData.title} Troubleshooting`,
                    `${this.cardData.title} Tips & Tricks`
                ]
            };
            
            // Match title to category
            for (const [category, suggestions] of Object.entries(templates)) {
                if (title.includes(category)) {
                    return suggestions;
                }
            }
            
            // Generic fallback
            return [
                `${this.cardData.title} Overview`,
                `${this.cardData.title} Examples`,
                `${this.cardData.title} FAQ`,
                `${this.cardData.title} Resources`,
                `${this.cardData.title} Case Studies`
            ];
        }
        
        displaySuggestions(suggestions) {
            const suggestionsGrid = this.element.querySelector('#suggestions-grid');
            
            const suggestionsHTML = suggestions.map(suggestion => `
                <div class="suggestion-item" data-title="${this.escapeHtml(suggestion)}">
                    <span class="suggestion-text">${this.escapeHtml(suggestion)}</span>
                    <button class="add-suggestion" title="Add this title">+</button>
                </div>
            `).join('');
            
            suggestionsGrid.innerHTML = suggestionsHTML;
            
            // Bind add suggestion buttons
            suggestionsGrid.querySelectorAll('.add-suggestion').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const title = e.target.closest('.suggestion-item').dataset.title;
                    this.addTitleToInput(title);
                });
            });
        }
        
        addTitleToInput(title) {
            const textarea = this.element.querySelector('#quickbulk-textarea');
            const currentValue = textarea.value.trim();
            const newValue = currentValue ? currentValue + '\n' + title : title;
            
            textarea.value = newValue;
            this.currentInput = newValue;
            this.updatePreview();
            
            // Scroll to show added title
            textarea.scrollTop = textarea.scrollHeight;
        }
        
        clearInput() {
            const textarea = this.element.querySelector('#quickbulk-textarea');
            textarea.value = '';
            this.currentInput = '';
            this.parsedPages = [];
            this.updatePreview();
            
            // Hide clipboard suggestion
            const suggestion = this.element.querySelector('#clipboard-suggestion');
            suggestion.style.display = 'none';
        }
        
        async handleSlugPaste() {
            try {
                if (navigator.clipboard) {
                    const text = await navigator.clipboard.readText();
                    const slugTextarea = this.element.querySelector('#quickbulk-slug-textarea');
                    slugTextarea.value = text;
                    this.currentSlugInput = text;
                    this.updatePreview();
                }
            } catch (error) {
                debug.warn('QuickBulk', 'Clipboard access failed:', error);
            }
        }

        generateSlugsFromTitles() {
            const titleLines = this.currentInput.split('\n')
                .map(line => line.trim())
                .filter(line => line.length > 0);
            
            const generatedSlugs = titleLines.map(title => this.generateSlug(title));
            const slugTextarea = this.element.querySelector('#quickbulk-slug-textarea');
            
            slugTextarea.value = generatedSlugs.join('\n');
            this.currentSlugInput = slugTextarea.value;
            this.updatePreview();
        }

        clearSlugs() {
            const slugTextarea = this.element.querySelector('#quickbulk-slug-textarea');
            slugTextarea.value = '';
            this.currentSlugInput = '';
            this.updatePreview();
        }
        
        editSlug(index) {
            const slugElement = this.element.querySelector(`.preview-slug[data-index="${index}"] .slug-value`);
            const currentSlug = this.parsedPages[index].slug;
            
            const input = document.createElement('input');
            input.type = 'text';
            input.value = currentSlug;
            input.className = 'slug-edit-input';
            
            input.addEventListener('blur', () => this.saveSlugEdit(index, input.value));
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.saveSlugEdit(index, input.value);
                }
            });
            
            slugElement.replaceWith(input);
            input.focus();
            input.select();
        }

        saveSlugEdit(index, newSlug) {
            // Update the parsed data
            this.parsedPages[index].slug = this.generateSlug(newSlug); // Sanitize the slug
            
            // Create properly sized slug array
            const titleCount = this.parsedPages.length;
            const slugLines = this.currentSlugInput ? this.currentSlugInput.split('\n') : [];
            
            // Ensure array is large enough
            while (slugLines.length < titleCount) {
                slugLines.push('');
            }
            
            // Update the specific slug
            slugLines[index] = this.parsedPages[index].slug;
            
            // Update both textarea and internal state
            const slugTextarea = this.element.querySelector('#quickbulk-slug-textarea');
            this.currentSlugInput = slugLines.join('\n');
            slugTextarea.value = this.currentSlugInput;
            
            // Refresh the preview
            this.updatePreviewList();
        }
        
        async createPages() {
            if (this.isCreating || this.parsedPages.length === 0) return;
            
            this.isCreating = true;
            this.showProgressOverlay();
            
            try {
                const result = await this.sendCreateRequest();
                if (result.success) {
                    this.showSuccess(result.data);
                    this.updateTreeVisualization(result.data.created);
                } else {
                    this.showError(result.data || 'Creation failed');
                }
            } catch (error) {
                this.showError('Network error occurred: ' + error.message);
            }
            
            this.isCreating = false;
        }
        
        showProgressOverlay() {
            const body = this.element.querySelector('.popup-body');
            const footer = this.element.querySelector('.popup-footer');
            const progress = this.element.querySelector('#creation-progress');
            
            body.style.display = 'none';
            footer.style.display = 'none';
            progress.style.display = 'block';
            
            // Animate progress
            this.animateProgress();
        }
        
        animateProgress() {
            const progressFill = this.element.querySelector('#progress-fill');
            const progressCurrent = this.element.querySelector('#progress-current');
            const progressTotal = this.element.querySelector('#progress-total');
            const progressDetails = this.element.querySelector('#progress-details');

            progressTotal.textContent = this.parsedPages.length;

            // Check if hierarchy data will be available (slugs contain slashes)
            const hasHierarchicalSlugs = this.parsedPages.some(page =>
                page.slug && page.slug.includes('/')
            );

            if (hasHierarchicalSlugs) {
                this.animateHierarchicalProgress(progressFill, progressCurrent, progressTotal, progressDetails);
            } else {
                this.animateStandardProgress(progressFill, progressCurrent, progressTotal, progressDetails);
            }
        }

        animateStandardProgress(progressFill, progressCurrent, progressTotal, progressDetails) {
            let current = 0;
            const interval = setInterval(() => {
                current++;
                progressCurrent.textContent = current;
                progressFill.style.width = Math.round((current / this.parsedPages.length) * 100) + '%';
                progressDetails.textContent = `Creating page: ${this.parsedPages[current - 1]?.title || 'Finishing up...'}`;

                if (current >= this.parsedPages.length) {
                    clearInterval(interval);
                    progressDetails.textContent = 'Finalizing pages and updating visualization...';
                }
            }, 200);
        }

        animateHierarchicalProgress(progressFill, progressCurrent, progressTotal, progressDetails) {
            // Group pages by hierarchy level
            const levelGroups = this.groupPagesByLevel(this.parsedPages);
            const maxLevel = Math.max(...Object.keys(levelGroups).map(Number));

            let currentLevel = 0;
            let currentPageInLevel = 0;
            let totalProcessed = 0;

            const interval = setInterval(() => {
                const currentLevelPages = levelGroups[currentLevel] || [];

                if (currentPageInLevel < currentLevelPages.length) {
                    // Processing page at current level
                    totalProcessed++;
                    currentPageInLevel++;

                    const levelPageCount = currentLevelPages.length;
                    const currentPage = currentLevelPages[currentPageInLevel - 1];

                    progressCurrent.textContent = totalProcessed;
                    progressFill.style.width = Math.round((totalProcessed / this.parsedPages.length) * 100) + '%';

                    // Show hierarchical progress details
                    if (currentLevel === 0) {
                        progressDetails.innerHTML = `
                            <div class="hierarchy-level">Creating Level ${currentLevel} pages (root level)</div>
                            <div class="current-page">Creating: ${currentPage?.title || 'Page'}</div>
                            <div class="level-progress">${currentPageInLevel} of ${levelPageCount} in this level</div>
                        `;
                    } else {
                        progressDetails.innerHTML = `
                            <div class="hierarchy-level">Creating Level ${currentLevel} pages</div>
                            <div class="current-page">Creating: ${currentPage?.title || 'Page'}</div>
                            <div class="level-progress">${currentPageInLevel} of ${levelPageCount} in this level</div>
                        `;
                    }
                } else {
                    // Move to next level
                    currentLevel++;
                    currentPageInLevel = 0;

                    if (currentLevel > maxLevel) {
                        // All levels complete
                        clearInterval(interval);
                        progressDetails.innerHTML = `
                            <div class="hierarchy-complete">✅ All hierarchy levels created</div>
                            <div class="finalizing">Finalizing pages and updating visualization...</div>
                        `;
                    }
                }
            }, 300); // Slightly slower for hierarchy to show level transitions
        }

        groupPagesByLevel(pages) {
            const levelGroups = {};

            pages.forEach(page => {
                const level = page.slug ? (page.slug.split('/').length - 1) : 0;
                if (!levelGroups[level]) {
                    levelGroups[level] = [];
                }
                levelGroups[level].push(page);
            });

            return levelGroups;
        }
        
        async sendCreateRequest() {
            const autoLink = this.element.querySelector('#auto-link').checked;
            const pageStatus = this.element.querySelector('#page-status').value;

            // Collect selected categories (for Posts only)
            const selectedCategories = [];
            if (this.cardData.post_type === 'post') {
                const categoryRadio = this.element.querySelector('input[name="bulk_categories"]:checked');
                if (categoryRadio) {
                    const categoryId = parseInt(categoryRadio.value);
                    selectedCategories.push(categoryId);
                }
            }

            const formData = new FormData();
            formData.append('action', 'slmm_quickbulk_create_pages');
            formData.append('nonce', slmmInterlinkingData?.nonce || '');
            formData.append('parent_id', this.cardData.id.replace(/^page_/, ''));
            formData.append('parent_title', this.cardData.title);
            formData.append('target_post_type', this.cardData.post_type || 'page'); // CRITICAL FIX: Send target post type

            // Add selected categories to form data
            if (selectedCategories.length > 0) {
                const categoriesJSON = JSON.stringify(selectedCategories);
                formData.append('categories', categoriesJSON);
            }
            
            // Send the original input text so backend can do intelligent processing
            // Rather than trying to reconstruct from parsed objects
            formData.append('page_titles', this.currentInput);
            formData.append('page_slugs', this.currentSlugInput || '');
            
            
            formData.append('auto_link', autoLink ? '1' : '0');
            formData.append('page_status', pageStatus);
            

            try {
                const response = await fetch(ajaxurl, {
                    method: 'POST',
                    body: formData
                });

                const responseText = await response.text();

                // Try to parse as JSON
                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('Failed to parse JSON response:', parseError);
                    throw new Error('Invalid JSON response from server');
                }

                return result;
            } catch (error) {
                console.error('Fetch error:', error);
                throw error;
            }
        }
        
        showSuccess(data) {
            const progress = this.element.querySelector('#creation-progress');
            progress.innerHTML = `
                <div class="success-content">
                    <div class="success-header">✅ Success!</div>
                    <div class="success-message">
                        Created ${data.success_count || this.parsedPages.length} pages successfully
                    </div>
                    <div class="success-actions">
                        <button id="close-success" class="success-btn">Close</button>
                        <button id="view-pages" class="success-btn secondary">View Pages</button>
                    </div>
                </div>
            `;
            
            // Bind success actions
            progress.querySelector('#close-success').addEventListener('click', () => {
                this.close();
            });
            
            progress.querySelector('#view-pages').addEventListener('click', () => {
                if (data.created && data.created[0]) {
                    window.open(data.created[0].edit_url, '_blank');
                }
            });
            
            // Auto-close after 5 seconds
            setTimeout(() => {
                if (this.isVisible) {
                    this.close();
                }
            }, 5000);
        }
        
        showError(error) {
            const progress = this.element.querySelector('#creation-progress');
            progress.innerHTML = `
                <div class="error-content">
                    <div class="error-header">❌ Error</div>
                    <div class="error-message">
                        ${this.escapeHtml(typeof error === 'string' ? error : 'Failed to create pages')}
                    </div>
                    <div class="error-actions">
                        <button id="retry-creation" class="error-btn">Try Again</button>
                        <button id="close-error" class="error-btn secondary">Close</button>
                    </div>
                </div>
            `;
            
            // Bind error actions
            progress.querySelector('#retry-creation').addEventListener('click', () => {
                this.hideProgressOverlay();
            });
            
            progress.querySelector('#close-error').addEventListener('click', () => {
                this.close();
            });
        }
        
        hideProgressOverlay() {
            const body = this.element.querySelector('.popup-body');
            const footer = this.element.querySelector('.popup-footer');
            const progress = this.element.querySelector('#creation-progress');
            
            progress.style.display = 'none';
            body.style.display = 'block';
            footer.style.display = 'block';
        }
        
        updateTreeVisualization(createdPages) {
            // CRITICAL FIX: Use the actual parent relationship from the response for proper tree placement


            // This will be handled by the tree integration
            if (window.slmmQuickBulkTreeIntegration) {
                // For posts with categories, use the visual parent_id from the response instead of this.cardData.id
                const visualParentId = createdPages.length > 0 && createdPages[0].parent_id
                    ? createdPages[0].parent_id
                    : this.cardData.id;

                window.slmmQuickBulkTreeIntegration.addCreatedPages(createdPages, visualParentId);
            }

            // Trigger custom event for external listeners
            document.dispatchEvent(new CustomEvent('slmmQuickBulkPagesCreated', {
                detail: {
                    parentCard: this.cardData,
                    createdPages: createdPages,
                    visualParentId: createdPages.length > 0 ? createdPages[0].parent_id : this.cardData.id
                }
            }));
        }
        
        close() {
            if (!this.isVisible) return;
            
            // CRITICAL: Clean up floating QuickBulk artifacts ONLY on manual close
            try {
                const treeGroup = d3.select('#slmm-tree-container svg g.tree-group');
                if (!treeGroup.empty()) {
                    treeGroup.selectAll('.node.new-node').remove();
                    debug.log('QuickBulk', 'QuickBulk artifacts cleaned up on manual popup close');
                }
            } catch (cleanupError) {
                debug.warn('QuickBulk', 'Error cleaning up QuickBulk artifacts:', cleanupError);
            }
            
            this.animateOut(() => {
                this.destroy();
            });
        }
        
        animateOut(callback) {
            this.element.style.opacity = '0';
            this.element.style.transform = 'scale(0.8) translateY(10px)';
            
            setTimeout(callback, 200);
        }
        
        destroy() {
            debug.log('QuickBulk', 'Destroying popup for:', this.cardData?.id);
            
            try {
                // Clear any pending timeouts
                if (this.debounceTimeout) {
                    clearTimeout(this.debounceTimeout);
                    this.debounceTimeout = null;
                }
                
                // Clear any interval timers
                if (this.updateInterval) {
                    clearInterval(this.updateInterval);
                    this.updateInterval = null;
                }
                
                // Remove event listeners to prevent memory leaks
                if (this.element) {
                    // Clone element to remove all event listeners
                    const newElement = this.element.cloneNode(true);
                    if (this.element.parentNode) {
                        this.element.parentNode.replaceChild(newElement, this.element);
                        // Then remove the cloned element
                        newElement.remove();
                    } else {
                        this.element.remove();
                    }
                    this.element = null;
                }
                
                // Enhanced trigger button state reset with validation
                if (this.triggerElement) {
                    try {
                        this.triggerElement.classList.remove('active');
                        
                        // Use the controller's resetTriggerState method for consistency
                        if (this.controller && typeof this.controller.resetTriggerState === 'function') {
                            this.controller.resetTriggerState(this.triggerElement);
                        } else {
                            // Fallback reset
                            this.triggerElement.style.background = 'linear-gradient(135deg, #3b82f6, #1d4ed8)';
                            this.triggerElement.style.opacity = '0';
                            this.triggerElement.style.transform = 'scale(0.9)';
                        }
                    } catch (triggerError) {
                        debug.warn('QuickBulk', 'Error resetting trigger state:', triggerError);
                    }
                }
                
                // Clear all object references
                this.isVisible = false;
                this.parentData = null;
                this.currentInput = '';
                this.currentPages = [];
                
                // Remove from controller's active popups with validation
                if (this.controller && this.cardData?.id) {
                    try {
                        // Check if popup is still in activePopups before attempting removal
                        if (this.controller.activePopups && this.controller.activePopups.has(this.cardData.id)) {
                            this.controller.activePopups.delete(this.cardData.id);
                            debug.log('QuickBulk', 'Removed popup from activePopups, remaining:', this.controller.activePopups.size);
                        }
                    } catch (controllerError) {
                        debug.warn('QuickBulk', 'Error removing from controller:', controllerError);
                    }
                }
                
                // Clear controller reference
                this.controller = null;
                this.cardData = null;
                this.triggerElement = null;
                
                debug.log('QuickBulk', 'Popup destruction complete');
                
            } catch (error) {
                debug.error('QuickBulk', 'Error during popup destruction:', error);
            }
        }
        
        // Get category selection HTML for Posts
        getCategorySelectionHTML() {
            // Only show category selection for Posts
            if (this.cardData.post_type !== 'post') {
                return '';
            }

            // Check if categories are available
            const categories = slmmQuickBulkData?.categories || [];
            if (categories.length === 0) {
                return '';
            }

            // CRITICAL FIX: Auto-select category when coming from category node
            let autoSelectedCategoryId = null;
            if (this.cardData.id && this.cardData.id.startsWith('category_')) {
                autoSelectedCategoryId = parseInt(this.cardData.id.replace('category_', ''));
            }

            return `
                <div class="category-selection">
                    <div class="category-header">
                        <span class="category-title">📁 Assign to Category</span>
                        <span class="category-subtitle">Select one category for these posts</span>
                        ${autoSelectedCategoryId ? `<span class="auto-selected-note">✅ Auto-selected: ${this.cardData.title}</span>` : ''}
                    </div>
                    <div class="category-radios">
                        ${categories.map(category => `
                            <label class="category-option">
                                <input type="radio" name="bulk_categories" value="${category.id}" ${category.id === autoSelectedCategoryId ? 'checked' : ''}>
                                <span class="category-name">${this.escapeHtml(category.name)}</span>
                                <span class="category-count">(${category.count})</span>
                            </label>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        // Utility methods
        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    }
    
    /**
     * Keyboard Handler Class
     */
    class SLMM_QuickBulk_KeyboardHandler {
        
        constructor(controller) {
            this.controller = controller;
            this.selectedNode = null;
            this.hoveredNode = null;
            
            this.init();
        }
        
        init() {
            document.addEventListener('keydown', this.handleKeyDown.bind(this));
            document.addEventListener('keyup', this.handleKeyUp.bind(this));
            
            // Track selected and hovered nodes
            this.trackNodeStates();
        }
        
        handleKeyDown(event) {
            // Skip if input is focused
            if (this.isInputFocused()) {
                return;
            }
            
            // Ctrl/Cmd + Shift + B = Quick bulk create from selected node
            if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'B') {
                event.preventDefault();
                this.triggerFromSelection();
                return;
            }
            
            // Space = Expand/collapse hovered node
            if (event.key === ' ' && this.hoveredNode) {
                event.preventDefault();
                this.triggerExpandCollapseFromHover();
                return;
            }
            
            // Single key shortcuts for node actions
            const key = event.key.toLowerCase();
            const targetNode = this.getTargetNode();
            
            if (!targetNode) {
                if (['d', 'e', 'v', 'b'].includes(key)) {
                    event.preventDefault();
                    this.showSelectionHint(key);
                }
                return;
            }
            
            switch (key) {
                case 'd':
                    event.preventDefault();
                    this.triggerDirectEdit(targetNode);
                    break;
                case 'e':
                    event.preventDefault();
                    this.triggerEdit(targetNode);
                    break;
                case 'v':
                    event.preventDefault();
                    this.triggerView(targetNode);
                    break;
                case 'b':
                    event.preventDefault();
                    this.triggerBulkAdd(targetNode);
                    break;
            }
        }
        
        handleKeyUp(event) {
            // Handle any key up events if needed
        }
        
        trackNodeStates() {
            // Track clicks for selection
            document.addEventListener('click', (event) => {
                const cardElement = event.target.closest('.slmm-tree-node, .tree-node, .node');
                if (cardElement && !event.target.closest('.slmm-quickbulk-trigger')) {
                    this.selectedNode = cardElement;
                }
            });
            
            // Track hover states
            document.addEventListener('mouseover', (event) => {
                const cardElement = event.target.closest('.slmm-tree-node, .tree-node, .node');
                if (cardElement) {
                    this.hoveredNode = cardElement;
                }
            });
            
            document.addEventListener('mouseout', (event) => {
                const cardElement = event.target.closest('.slmm-tree-node, .tree-node, .node');
                if (cardElement === this.hoveredNode) {
                    this.hoveredNode = null;
                }
            });
        }
        
        triggerFromSelection() {
            if (this.selectedNode) {
                const trigger = this.selectedNode.querySelector('.slmm-quickbulk-trigger');
                if (trigger) {
                    trigger.click();
                }
            } else {
                this.showSelectionHint();
            }
        }
        
        triggerFromHover() {
            if (this.hoveredNode) {
                const trigger = this.hoveredNode.querySelector('.slmm-quickbulk-trigger');
                if (trigger) {
                    trigger.click();
                }
            }
        }
        
        /**
         * Trigger expand/collapse for hovered node (replaces QuickBulk on spacebar)
         */
        triggerExpandCollapseFromHover() {
            if (this.hoveredNode) {
                // Get D3.js data bound to the hovered node
                const nodeData = this.hoveredNode.__data__;
                
                if (!nodeData) {
                    debug.log('Keyboard Shortcuts', 'No D3.js data found on hovered node');
                    return;
                }
                
                // Check if node has children to expand/collapse
                if (!nodeData.data || !nodeData.data.hasChildren) {
                    debug.log('Keyboard Shortcuts', 'Node has no children to expand/collapse');
                    return;
                }
                
                // Get the current expanded state
                const isExpanded = nodeData.data.isExpanded;
                
                debug.log('Keyboard Shortcuts', 'Node current state - isExpanded:', isExpanded, 'for:', nodeData.data.name);
                
                // Try to trigger the D3.js click event on the expand button
                const expandButton = this.hoveredNode.querySelector('.slmm-expand-button, .slmm-expand-symbol');
                if (expandButton) {
                    // Create and dispatch a proper click event that D3.js can handle
                    const event = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    });
                    
                    // Trigger the event on the expand button
                    expandButton.dispatchEvent(event);
                    debug.log('Keyboard Shortcuts', 'Expand/collapse event dispatched for:', nodeData.data.name);
                } else {
                    // Fallback: Try to access global expand/collapse functions if available
                    if (window.expandNode && window.collapseNode) {
                        if (isExpanded) {
                            window.collapseNode(nodeData);
                            debug.log('Keyboard Shortcuts', 'Collapsed node via global function:', nodeData.data.name);
                        } else {
                            window.expandNode(nodeData);
                            debug.log('Keyboard Shortcuts', 'Expanded node via global function:', nodeData.data.name);
                        }
                    } else {
                        debug.warn('Keyboard Shortcuts', 'No expand button found and global functions not available');
                    }
                }
            }
        }
        
        /**
         * Get the target node for keyboard actions (selected node has priority over hovered)
         */
        getTargetNode() {
            return this.selectedNode || this.hoveredNode;
        }
        
        /**
         * Get D3.js data from node element
         */
        getNodeData(nodeElement) {
            return nodeElement && nodeElement.__data__ ? nodeElement.__data__.data : null;
        }
        
        /**
         * Trigger Direct Edit popup for the target node
         */
        triggerDirectEdit(nodeElement) {
            const nodeData = this.getNodeData(nodeElement);
            if (!nodeData || nodeData.post_type === 'site') {
                this.showActionHint('Direct Edit not available for this node type');
                return;
            }
            
            // Look for direct edit button and trigger it
            const directEditButton = nodeElement.querySelector('.slmm-node-directedit-button');
            if (directEditButton) {
                directEditButton.dispatchEvent(new MouseEvent('click', { bubbles: true }));
                debug.log('Keyboard Shortcuts', 'Direct Edit triggered for:', nodeData.name);
            } else {
                // Fallback: Call the direct editor function directly if available
                if (typeof window.openDirectEditor === 'function') {
                    window.openDirectEditor(nodeData.id);
                    debug.log('Keyboard Shortcuts', 'Direct Edit triggered via function for:', nodeData.name);
                } else {
                    this.showActionHint('Direct Editor not available');
                }
            }
        }
        
        /**
         * Trigger Edit action (open WordPress admin) for the target node  
         */
        triggerEdit(nodeElement) {
            const nodeData = this.getNodeData(nodeElement);
            if (!nodeData || !nodeData.id || nodeData.post_type === 'site') {
                this.showActionHint('Edit not available for this node');
                return;
            }
            
            // Look for edit button and trigger it
            const editButton = nodeElement.querySelector('.slmm-node-edit-button');
            if (editButton) {
                editButton.dispatchEvent(new MouseEvent('click', { bubbles: true }));
                debug.log('Keyboard Shortcuts', 'Edit triggered for:', nodeData.name);
            } else {
                // Fallback: Construct the edit URL directly
                const editUrl = `${window.slmmInterlinkingData?.site_url || window.location.origin}/wp-admin/edit.php?post_type=${nodeData.post_type}&page=${nodeData.id}`;
                window.open(editUrl, '_blank');
                debug.log('Keyboard Shortcuts', 'Edit triggered via URL for:', nodeData.name);
            }
        }
        
        /**
         * Trigger View action (open front-end) for the target node
         */
        triggerView(nodeElement) {
            const nodeData = this.getNodeData(nodeElement);
            if (!nodeData || !nodeData.permalink) {
                this.showActionHint('View not available - no permalink found');
                return;
            }
            
            // Look for view button and trigger it
            const viewButton = nodeElement.querySelector('.slmm-node-view-button');
            if (viewButton) {
                viewButton.dispatchEvent(new MouseEvent('click', { bubbles: true }));
                debug.log('Keyboard Shortcuts', 'View triggered for:', nodeData.name);
            } else {
                // Fallback: Open the permalink directly
                window.open(nodeData.permalink, '_blank');
                debug.log('Keyboard Shortcuts', 'View triggered via permalink for:', nodeData.name);
            }
        }
        
        /**
         * Trigger Bulk Add (QuickBulk) for the target node
         */
        triggerBulkAdd(nodeElement) {
            const nodeData = this.getNodeData(nodeElement);
            if (!nodeData || !nodeData.id || nodeData.post_type === 'site') {
                this.showActionHint('Bulk Add not available for this node');
                return;
            }
            
            // Look for quickbulk button and trigger it
            const quickbulkButton = nodeElement.querySelector('.slmm-node-quickbulk-button, .slmm-quickbulk-trigger');
            if (quickbulkButton) {
                quickbulkButton.dispatchEvent(new MouseEvent('click', { bubbles: true }));
                debug.log('Keyboard Shortcuts', 'QuickBulk triggered for:', nodeData.name);
            } else {
                // Fallback: Call QuickBulk controller directly if available
                if (window.slmmQuickBulkController && typeof window.slmmQuickBulkController.showPopup === 'function') {
                    // Get the position of the node for popup placement
                    const rect = nodeElement.getBoundingClientRect();
                    window.slmmQuickBulkController.showPopup(nodeData, rect.right, rect.top);
                    debug.log('Keyboard Shortcuts', 'QuickBulk triggered via controller for:', nodeData.name);
                } else {
                    this.showActionHint('QuickBulk not available');
                }
            }
        }
        
        showSelectionHint(key = null) {
            const hint = document.createElement('div');
            hint.className = 'slmm-keyboard-hint';
            
            let message = '';
            if (key) {
                const shortcuts = {
                    'd': 'Direct Edit',
                    'e': 'Edit in WordPress Admin',
                    'v': 'View Front-end',
                    'b': 'Bulk Quick Add'
                };
                message = `💡 First select a page in the tree, then press "${key.toUpperCase()}" for ${shortcuts[key]}`;
            } else {
                message = `
                    💡 First select a page in the tree, then use keyboard shortcuts:<br>
                    <strong>D</strong> = Direct Edit &nbsp;&nbsp; <strong>E</strong> = Edit &nbsp;&nbsp; <strong>V</strong> = View &nbsp;&nbsp; <strong>B</strong> = Bulk Add
                `;
            }
            
            hint.innerHTML = `<div class="hint-content">${message}</div>`;
            hint.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: #1a1a1a;
                color: #f3f4f6;
                padding: 15px 25px;
                border-radius: 8px;
                z-index: 100002;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
                animation: fadeInOut 3s ease-in-out forwards;
                max-width: 400px;
                text-align: center;
                line-height: 1.4;
            `;
            
            document.body.appendChild(hint);
            setTimeout(() => hint.remove(), 3000);
        }
        
        /**
         * Show action-specific hint messages
         */
        showActionHint(message) {
            const hint = document.createElement('div');
            hint.className = 'slmm-action-hint';
            hint.innerHTML = `<div class="hint-content">⚠️ ${message}</div>`;
            hint.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #dc2626;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                z-index: 100002;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                font-size: 14px;
                font-weight: 500;
                animation: slideInFade 2.5s ease-in-out forwards;
            `;
            
            document.body.appendChild(hint);
            setTimeout(() => hint.remove(), 2500);
        }
        
        isInputFocused() {
            const activeElement = document.activeElement;
            return activeElement && (
                activeElement.tagName === 'INPUT' ||
                activeElement.tagName === 'TEXTAREA' ||
                activeElement.contentEditable === 'true'
            );
        }
    }
    
    // CSS Injection for styling
    const injectCSS = () => {
        if (document.querySelector('#slmm-quickbulk-styles')) return;
        
        const styles = document.createElement('style');
        styles.id = 'slmm-quickbulk-styles';
        styles.textContent = `
            /* QuickBulk Widget Styles */
            .slmm-quickbulk-widget {
                pointer-events: auto;
            }
            
            .slmm-quickbulk-trigger {
                font-family: inherit !important;
            }
            
            /* Popup Styles */
            .slmm-quickbulk-popup * {
                box-sizing: border-box;
            }
            
            .slmm-quickbulk-popup .popup-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-bottom: 12px;
                border-bottom: 1px solid #374151;
                margin-bottom: 16px;
            }
            
            .slmm-quickbulk-popup .parent-context {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 14px;
            }
            
            .slmm-quickbulk-popup .close-btn {
                background: none;
                border: none;
                color: #9ca3af;
                font-size: 20px;
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                transition: color 0.2s;
            }
            
            .slmm-quickbulk-popup .close-btn:hover {
                color: #f3f4f6;
                background: #374151;
            }
            
            .slmm-quickbulk-popup .dual-input-container {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 12px;
                margin-bottom: 16px;
            }
            
            .slmm-quickbulk-popup .input-column {
                /* Grid items - no flex needed */
            }
            
            .slmm-quickbulk-popup .titles-column {
                /* Grid item - no flex needed */
            }
            
            .slmm-quickbulk-popup .slugs-column {
                margin-bottom: 12px;
            }
            
            .slmm-quickbulk-popup .input-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
            }
            
            .slmm-quickbulk-popup .input-actions {
                display: flex;
                gap: 4px;
            }
            
            .slmm-quickbulk-popup .quick-action {
                background: #374151;
                border: 1px solid #4b5563;
                color: #f3f4f6;
                padding: 4px 8px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                transition: background 0.2s;
            }
            
            .slmm-quickbulk-popup .quick-action:hover {
                background: #4b5563;
            }
            
            .slmm-quickbulk-popup .quickbulk-input {
                width: 100%;
                background: #374151;
                border: 1px solid #4b5563;
                color: #f3f4f6;
                border-radius: 6px;
                padding: 12px;
                font-family: monospace;
                font-size: 14px;
                resize: vertical;
                margin-bottom: 12px;
            }
            
            .slmm-quickbulk-popup .quickbulk-input:focus {
                outline: none;
                border-color: #3b82f6;
                box-shadow: 0 0 0 1px #3b82f6;
            }
            
            .slmm-quickbulk-popup .clipboard-suggestion {
                background: #374151;
                border: 1px solid #10b981;
                border-radius: 6px;
                padding: 12px;
                margin-bottom: 12px;
            }
            
            .slmm-quickbulk-popup .suggestion-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                font-size: 12px;
                font-weight: 500;
            }
            
            .slmm-quickbulk-popup .use-clipboard-btn {
                background: #10b981;
                border: none;
                color: white;
                padding: 4px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
            }
            
            .slmm-quickbulk-popup .suggestion-preview {
                font-family: monospace;
                font-size: 12px;
                color: #9ca3af;
                white-space: pre-line;
                max-height: 60px;
                overflow: hidden;
            }
            
            .slmm-quickbulk-popup .preview-section {
                margin: 16px 0;
            }
            
            .slmm-quickbulk-popup .preview-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                font-size: 12px;
                font-weight: 500;
            }
            
            .slmm-quickbulk-popup .preview-list {
                max-height: 120px;
                overflow-y: auto;
                background: #111827;
                border-radius: 4px;
                padding: 8px;
            }
            
            .slmm-quickbulk-popup .preview-item {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 4px 0;
                font-size: 12px;
                border-bottom: 1px solid #374151;
            }
            
            .slmm-quickbulk-popup .preview-item:last-child {
                border-bottom: none;
            }
            
            .slmm-quickbulk-popup .preview-number {
                color: #6b7280;
                min-width: 20px;
            }
            
            .slmm-quickbulk-popup .preview-title {
                flex: 1;
                color: #f3f4f6;
            }
            
            .slmm-quickbulk-popup .slug-input {
                background: #2d3748;
                border: 1px solid #4a5568;
                margin-bottom: 8px;
            }

            .slmm-quickbulk-popup .preview-content {
                display: flex;
                flex-direction: column;
                flex: 1;
            }

            .slmm-quickbulk-popup .preview-title {
                font-weight: 500;
                color: #f3f4f6;
            }

            .slmm-quickbulk-popup .preview-slug {
                font-size: 11px;
                color: #a0aec0;
                margin-top: 4px;
            }

            .slmm-quickbulk-popup .slug-label {
                margin-right: 4px;
            }

            .slmm-quickbulk-popup .slug-value.editable {
                cursor: pointer;
                padding: 2px 4px;
                border-radius: 3px;
                transition: background-color 0.2s;
                color: #9ca3af;
                font-family: monospace;
            }

            .slmm-quickbulk-popup .slug-value.editable:hover {
                background: #4a5568;
                color: #e2e8f0;
            }

            .slmm-quickbulk-popup .slug-edit-input {
                background: #1a202c;
                border: 1px solid #3182ce;
                color: #e2e8f0;
                padding: 2px 4px;
                border-radius: 3px;
                font-size: 11px;
                width: 200px;
                font-family: monospace;
            }
            
            
            .slmm-quickbulk-popup .empty-state {
                text-align: center;
                color: #6b7280;
                font-style: italic;
                padding: 20px;
            }
            
            .slmm-quickbulk-popup .ai-suggestions {
                margin: 16px 0;
                background: #111827;
                border-radius: 6px;
                padding: 12px;
            }
            
            .slmm-quickbulk-popup .suggestions-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;
                font-size: 12px;
                font-weight: 500;
            }
            
            .slmm-quickbulk-popup .suggestions-grid {
                display: grid;
                gap: 8px;
            }
            
            .slmm-quickbulk-popup .suggestion-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                background: #374151;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
            }
            
            .slmm-quickbulk-popup .add-suggestion {
                background: #3b82f6;
                border: none;
                color: white;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                cursor: pointer;
                font-size: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .slmm-quickbulk-popup .popup-footer {
                padding-top: 16px;
                border-top: 1px solid #374151;
            }
            
            .slmm-quickbulk-popup .creation-options {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;
                font-size: 12px;
            }
            
            .slmm-quickbulk-popup .option {
                display: flex;
                align-items: center;
                gap: 6px;
                cursor: pointer;
            }

            /* Category Selection Styles */
            .slmm-quickbulk-popup .category-selection {
                margin: 12px 0;
                padding: 12px;
                background: #1f2937;
                border: 1px solid #374151;
                border-radius: 6px;
            }

            .slmm-quickbulk-popup .category-header {
                margin-bottom: 8px;
            }

            .slmm-quickbulk-popup .category-title {
                font-weight: 500;
                color: #f9fafb;
                font-size: 13px;
            }

            .slmm-quickbulk-popup .category-subtitle {
                font-size: 11px;
                color: #9ca3af;
                margin-left: 8px;
            }

            .slmm-quickbulk-popup .auto-selected-note {
                display: block;
                font-size: 11px;
                color: #10b981;
                margin-top: 4px;
                font-weight: 500;
            }

            .slmm-quickbulk-popup .category-radios {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 8px;
                max-height: 150px;
                overflow-y: auto;
            }

            .slmm-quickbulk-popup .category-option {
                display: flex;
                align-items: center;
                gap: 6px;
                cursor: pointer;
                padding: 4px;
                border-radius: 3px;
                font-size: 12px;
                transition: background 0.2s;
            }

            .slmm-quickbulk-popup .category-option:hover {
                background: #374151;
            }

            .slmm-quickbulk-popup .category-name {
                color: #e5e7eb;
            }

            .slmm-quickbulk-popup .category-count {
                color: #6b7280;
                font-size: 10px;
            }
            
            .slmm-quickbulk-popup .status-select {
                background: #374151;
                border: 1px solid #4b5563;
                color: #f3f4f6;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
            }
            
            .slmm-quickbulk-popup .create-btn {
                width: 100%;
                background: #10b981;
                border: none;
                color: white;
                padding: 12px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                transition: background 0.2s;
            }
            
            .slmm-quickbulk-popup .create-btn:hover:not(:disabled) {
                background: #059669;
            }
            
            .slmm-quickbulk-popup .create-btn:disabled {
                background: #374151;
                color: #6b7280;
                cursor: not-allowed;
            }
            
            .slmm-quickbulk-popup .progress-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: #1a1a1a;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .slmm-quickbulk-popup .progress-content {
                text-align: center;
                width: 100%;
                padding: 20px;
            }
            
            .slmm-quickbulk-popup .progress-header {
                font-size: 16px;
                font-weight: 500;
                margin-bottom: 16px;
            }
            
            .slmm-quickbulk-popup .progress-bar {
                width: 100%;
                height: 8px;
                background: #374151;
                border-radius: 4px;
                overflow: hidden;
                margin-bottom: 12px;
            }
            
            .slmm-quickbulk-popup .progress-fill {
                height: 100%;
                background: #10b981;
                transition: width 0.3s ease;
            }
            
            .slmm-quickbulk-popup .progress-details {
                font-size: 12px;
                color: #9ca3af;
                margin-bottom: 8px;
            }
            
            .slmm-quickbulk-popup .progress-stats {
                font-size: 14px;
                font-weight: 500;
            }
            
            .slmm-quickbulk-popup .success-content,
            .slmm-quickbulk-popup .error-content {
                text-align: center;
                padding: 20px;
            }
            
            .slmm-quickbulk-popup .success-header {
                font-size: 18px;
                color: #10b981;
                margin-bottom: 12px;
            }
            
            .slmm-quickbulk-popup .error-header {
                font-size: 18px;
                color: #ef4444;
                margin-bottom: 12px;
            }
            
            .slmm-quickbulk-popup .success-actions,
            .slmm-quickbulk-popup .error-actions {
                display: flex;
                gap: 12px;
                justify-content: center;
                margin-top: 16px;
            }
            
            .slmm-quickbulk-popup .success-btn,
            .slmm-quickbulk-popup .error-btn {
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                border: 1px solid;
            }
            
            .slmm-quickbulk-popup .success-btn {
                background: #10b981;
                border-color: #10b981;
                color: white;
            }
            
            .slmm-quickbulk-popup .error-btn {
                background: #ef4444;
                border-color: #ef4444;
                color: white;
            }
            
            .slmm-quickbulk-popup .success-btn.secondary,
            .slmm-quickbulk-popup .error-btn.secondary {
                background: transparent;
                color: #9ca3af;
            }
            
            @keyframes fadeInOut {
                0% { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
                15% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                85% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                100% { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
            }
            
            @keyframes slideInFade {
                0% { opacity: 0; transform: translateX(100px); }
                15% { opacity: 1; transform: translateX(0); }
                85% { opacity: 1; transform: translateX(0); }
                100% { opacity: 0; transform: translateX(50px); }
            }
            
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            
            /* Dragging styles for improved UX */
            .slmm-quickbulk-popup.dragging {
                opacity: 0.9;
                z-index: 100002;
                user-select: none;
                transition: none;
            }
            
            .slmm-quickbulk-popup .popup-header:hover {
                background: rgba(55, 65, 81, 0.5);
                border-radius: 6px 6px 0 0;
                transition: background 0.2s ease;
            }
            
            .slmm-quickbulk-popup.dragging .popup-header {
                background: rgba(55, 65, 81, 0.8);
            }
        `;
        
        document.head.appendChild(styles);
    };
    
    // Global validation commands for console testing
    window.validateQuickBulk = function() {
        if (window.slmmQuickBulkController) {
            return window.slmmQuickBulkController.validateSystem();
        } else {
            debug.error('QuickBulk Validation', 'QuickBulk controller not available for validation');
            return { error: 'Controller not available' };
        }
    };
    
    window.quickBulkHealthCheck = function() {
        if (window.slmmQuickBulkController) {
            return window.slmmQuickBulkController.healthCheck();
        } else {
            debug.error('QuickBulk Debug', 'QuickBulk controller not available for health check');
            return { error: 'Controller not available' };
        }
    };
    
    window.debugQuickBulk = function() {
        debug.info('QuickBulk Debug', 'SLMM QuickBulk Debug Information:');
        debug.info('QuickBulk Debug', 'Controller available:', typeof window.slmmQuickBulkController !== 'undefined');
        debug.info('QuickBulk Debug', 'Active popups:', window.slmmQuickBulkController?.activePopups?.size || 0);
        debug.info('QuickBulk Debug', 'Widget count:', document.querySelectorAll('.slmm-quickbulk-widget').length);
        debug.info('QuickBulk Debug', 'Node count:', document.querySelectorAll('.slmm-tree-node, .tree-node, .node').length);
        debug.info('QuickBulk Debug', 'QuickBulk data available:', typeof slmmQuickBulkData !== 'undefined');
        
        // Keyboard shortcuts debug info
        debug.info('Keyboard Help', 'SLMM Keyboard Shortcuts Available:');
        debug.info('Keyboard Help', 'D = Direct Edit popup');
        debug.info('Keyboard Help', 'E = Edit in WordPress admin');
        debug.info('Keyboard Help', 'V = View front-end'); 
        debug.info('Keyboard Help', 'B = Bulk Quick Add');
        debug.info('Keyboard Help', 'Space = Quick create from hovered node');
        debug.info('Keyboard Help', 'Ctrl+Shift+B = Quick bulk create from selected node');
        
        if (window.slmmQuickBulkController?.keyboardHandler) {
            const handler = window.slmmQuickBulkController.keyboardHandler;
            debug.info('QuickBulk Debug', 'Keyboard handler status:');
            debug.info('QuickBulk Debug', 'Selected node:', handler.selectedNode ? 'Available' : 'None');
            debug.info('QuickBulk Debug', 'Hovered node:', handler.hoveredNode ? 'Available' : 'None');
        }
        
        if (window.slmmQuickBulkController) {
            return window.slmmQuickBulkController.healthCheck();
        }
        
        return { message: 'Debug information logged to console' };
    };

    // Initialize when DOM is ready
    const initialize = () => {
        // Inject CSS first
        injectCSS();
        
        // Create global controller instance
        window.slmmQuickBulkController = new SLMM_QuickBulk_Controller();
        
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('QuickBulk Canvas Integration loaded', null, 'quickbulk');
            SlmmDebugLogger.log('Console commands available: validateQuickBulk(), quickBulkHealthCheck(), debugQuickBulk()', null, 'quickbulk');
        }
    };
    
    // Initialize based on document state
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
})(jQuery, d3);