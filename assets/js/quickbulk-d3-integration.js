/**
 * SLMM QuickBulk D3.js Tree Integration
 * Handles real-time updates to the D3.js tree visualization when pages are created via QuickBulk
 * 
 * @version 1.0.0
 * <AUTHOR> SEO Bundle
 */

(function($, d3) {
    'use strict';
    
    // Ensure dependencies are available
    if (typeof $ === 'undefined' || typeof d3 === 'undefined') {
        if (window.SLMM && window.SLMM.debug) {
            window.SLMM.debug.error('QuickBulk D3', 'Missing dependencies (jQuery or D3.js)');
        }
        return;
    }
    
    /**
     * QuickBulk Tree Integration Class
     * Manages the addition of new nodes to the D3.js tree visualization
     */
    class SLMM_QuickBulk_TreeIntegration {
        
        constructor(d3TreeInstance) {
            this.tree = d3TreeInstance;
            this.svg = null;
            this.simulation = null;
            this.animationQueue = [];
            this.isAnimating = false;

            // MEMORY LEAK PROTECTION (CRITICAL FIX)
            this.timeouts = []; // Track all timeouts for cleanup
            this.eventListeners = []; // Track event listeners for cleanup
            this.isDestroyed = false; // Prevent operations after cleanup
            this.createdNodes = new Set(); // Track created nodes for cleanup
            this.globalDataArrays = []; // Track global arrays that need cleanup

            // Configuration
            this.config = {
                newNodeColor: '#10b981', // Green for new pages
                parentHighlightColor: '#3b82f6', // Blue for parent highlight
                animationDuration: 600,
                staggerDelay: 150,
                maxAnimationDelay: 3000,
                nodeSize: 8,
                parentNodePulseSize: 12
            };
            
            this.init();
        }
        
        init() {
            this.findTreeElements();
            this.bindEventListeners();
            
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.log('QuickBulk D3', 'Integration initialized');
            }
        }
        
        /**
         * Find and store references to D3.js tree elements
         */
        findTreeElements() {
            // Try to find the SVG element
            this.svg = d3.select('.slmm-tree-svg, .interlinking-tree-svg, .tree-svg');
            
            if (this.svg.empty()) {
                if (window.SLMM && window.SLMM.debug) {
                    window.SLMM.debug.warn('QuickBulk D3', 'Tree SVG not found, retrying...');
                }
                setTimeout(() => this.findTreeElements(), 1000);
                return;
            }
            
            // Try to find the simulation instance
            if (window.slmmInterlinkingSimulation) {
                this.simulation = window.slmmInterlinkingSimulation;
            } else if (this.tree && this.tree.simulation) {
                this.simulation = this.tree.simulation;
            }
            
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.log('QuickBulk D3', 'Tree elements found');
            }
        }
        
        /**
         * Bind event listeners for page creation events
         */
        bindEventListeners() {
            // Listen for bulk page creation events
            document.addEventListener('slmmQuickBulkPagesCreated', (event) => {
                this.handlePagesCreated(event.detail);
            });
            
            // Listen for single page creation events
            document.addEventListener('slmmPageCreated', (event) => {
                this.handleSinglePageCreated(event.detail);
            });
        }
        
        /**
         * Handle bulk pages created event
         * 
         * @param {Object} eventData Event data containing created pages and parent info
         */
        handlePagesCreated(eventData) {
            if (!eventData.createdPages || eventData.createdPages.length === 0) {
                if (window.SLMM && window.SLMM.debug) {
                    window.SLMM.debug.warn('QuickBulk D3', 'No pages in creation event');
                }
                return;
            }
            
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.log('QuickBulk D3', `Adding ${eventData.createdPages.length} pages to tree`);
            }
            
            this.addCreatedPages(eventData.createdPages, eventData.parentCard);
        }
        
        /**
         * Handle single page created event
         * 
         * @param {Object} eventData Event data for single page
         */
        handleSinglePageCreated(eventData) {
            if (!eventData.page) {
                if (window.SLMM && window.SLMM.debug) {
                    window.SLMM.debug.warn('QuickBulk D3', 'No page data in single creation event');
                }
                return;
            }
            
            this.addCreatedPages([eventData.page], eventData.parentCard);
        }
        
        /**
         * Add newly created pages to the D3.js visualization
         * 
         * @param {Array} createdPages Array of created page data
         * @param {Object} parentCard Parent card information
         */
        addCreatedPages(createdPages, parentCard) {
            if (this.svg.empty()) {
                if (window.SLMM && window.SLMM.debug) {
                    window.SLMM.debug.warn('QuickBulk D3', 'Tree not ready, retrying...');
                }
                setTimeout(() => this.addCreatedPages(createdPages, parentCard), 500);
                return;
            }
            
            try {
                const parentNode = this.findParentNode(parentCard);
                
                // If parent node is invalid, clean up any existing artifacts and abort
                if (!parentNode) {
                    if (window.SLMM && window.SLMM.debug) {
                        window.SLMM.debug.warn('QuickBulk D3', 'Parent node not found, cleaning up artifacts');
                    }
                    this.cleanupArtifacts();
                    return;
                }
                
                const newNodes = this.prepareNewNodes(createdPages, parentNode);
                
                // Queue the animation
                this.queueAnimation({
                    type: 'add_nodes',
                    nodes: newNodes,
                    parent: parentNode,
                    timestamp: Date.now()
                });
                
            } catch (error) {
                if (window.SLMM && window.SLMM.debug) {
                    window.SLMM.debug.error('QuickBulk D3', 'Error adding pages', error);
                }
                // Clean up artifacts on any error
                this.cleanupArtifacts();
            }
        }
        
        /**
         * Find the parent node in the D3.js tree
         * 
         * @param {Object} parentCard Parent card data
         * @return {Object|null} Parent node data
         */
        findParentNode(parentCard) {
            if (!parentCard || !parentCard.id) {
                if (window.SLMM && window.SLMM.debug) {
                    window.SLMM.debug.warn('QuickBulk D3', 'Invalid parent card data');
                }
                return null;
            }
            
            // Try different methods to find the parent node
            let parentNode = null;
            
            // Method 1: Find by data-bound element
            const parentElement = document.querySelector(`[data-page-id="${parentCard.id}"], [data-node-id="${parentCard.id}"]`);
            if (parentElement && parentElement.__data__) {
                parentNode = parentElement.__data__;
            }
            
            // Method 2: Search through tree data
            if (!parentNode && this.tree && this.tree.nodes) {
                parentNode = this.tree.nodes.find(node => 
                    node.id === parentCard.id || 
                    node.id === `page_${parentCard.id}` ||
                    node.page_id === parentCard.id
                );
            }
            
            // Method 3: Search through D3 selection data
            if (!parentNode) {
                this.svg.selectAll('.node, .tree-node').each(function(d) {
                    if (d && (d.id === parentCard.id || d.id === `page_${parentCard.id}`)) {
                        parentNode = d;
                    }
                });
            }
            
            return parentNode;
        }
        
        /**
         * Prepare new node data for D3.js visualization
         * 
         * @param {Array} createdPages Created page data
         * @param {Object} parentNode Parent node data
         * @return {Array} Prepared node data
         */
        prepareNewNodes(createdPages, parentNode) {
            return createdPages.map((page, index) => {
                const nodeId = `page_${page.id}`;
                
                // Calculate initial position near parent
                let initialX = 200;
                let initialY = 200;
                
                if (parentNode) {
                    initialX = parentNode.x ? parentNode.x + (Math.random() * 60 - 30) : initialX;
                    initialY = parentNode.y ? parentNode.y + 80 + (index * 20) : initialY + (index * 20);
                }
                
                const newNode = {
                    // Basic node properties
                    id: nodeId,
                    page_id: page.id,
                    title: page.title,
                    url: page.url,
                    edit_url: page.edit_url,
                    status: page.status,
                    
                    // Tree relationships
                    parent: parentNode ? parentNode.id : null,
                    children: [],
                    depth: parentNode ? (parentNode.depth || 0) + 1 : 1,
                    
                    // Position properties
                    x: initialX,
                    y: initialY,
                    fx: null, // Fixed X (for dragging)
                    fy: null, // Fixed Y (for dragging)
                    
                    // Visual properties
                    size: this.config.nodeSize,
                    color: this.config.newNodeColor,
                    opacity: 0, // Start invisible for animation
                    
                    // Animation properties
                    isNew: true,
                    creationIndex: index,
                    animationDelay: index * this.config.staggerDelay,
                    
                    // SEO metadata
                    seo_score: 45, // Default for new pages
                    authority_score: 0.1,
                    content_depth: 'minimal',
                    created_via_bulk: true,
                    created_at: new Date(),
                    
                    // QuickBulk capability
                    quickBulk: {
                        enabled: true,
                        contextKeywords: this.extractKeywords(page.title),
                        parentId: page.id,
                        hierarchyLevel: parentNode ? (parentNode.hierarchyLevel || 0) + 1 : 1
                    },
                    
                    // D3.js specific properties
                    index: null, // Will be set by D3
                    vx: 0, // Velocity X
                    vy: 0  // Velocity Y
                };
                
                return newNode;
            });
        }
        
        /**
         * Extract keywords from page title for context
         * 
         * @param {string} title Page title
         * @return {Array} Array of keywords
         */
        extractKeywords(title) {
            return title
                .toLowerCase()
                .replace(/[^\w\s]/g, ' ')
                .split(/\s+/)
                .filter(word => word.length > 3)
                .slice(0, 5);
        }
        
        /**
         * Queue animation for processing
         * 
         * @param {Object} animation Animation data
         */
        queueAnimation(animation) {
            this.animationQueue.push(animation);
            
            if (!this.isAnimating) {
                this.processAnimationQueue();
            }
        }
        
        /**
         * Process queued animations
         */
        async processAnimationQueue() {
            if (this.animationQueue.length === 0) {
                this.isAnimating = false;
                return;
            }
            
            this.isAnimating = true;
            const animation = this.animationQueue.shift();
            
            try {
                switch (animation.type) {
                    case 'add_nodes':
                        await this.animateNodeAddition(animation);
                        break;
                    default:
                        if (window.SLMM && window.SLMM.debug) {
                            window.SLMM.debug.warn('QuickBulk D3', 'Unknown animation type: ' + animation.type);
                        }
                }
            } catch (error) {
                if (window.SLMM && window.SLMM.debug) {
                    window.SLMM.debug.error('QuickBulk D3', 'Animation error', error);
                }
            }
            
            // Process next animation after a brief delay
            setTimeout(() => this.processAnimationQueue(), 100);
        }
        
        /**
         * Animate the addition of new nodes to the tree
         * 
         * @param {Object} animation Animation data
         */
        async animateNodeAddition(animation) {
            const { nodes, parent } = animation;
            
            // Add nodes to existing data structure
            this.addNodesToTreeData(nodes);
            
            // Create visual elements
            this.createNodeElements(nodes);
            
            // Update parent node to show it has new children
            if (parent) {
                this.highlightParentNode(parent);
                this.updateParentChildRelationship(parent, nodes);
            }
            
            // Animate node entrance with stagger effect
            this.animateNodeEntrance(nodes);
            
            // Update D3.js simulation if available
            this.updateSimulation(nodes);
            
            // Wait for animations to complete
            const maxDelay = Math.max(...nodes.map(n => n.animationDelay)) + this.config.animationDuration;
            await this.sleep(Math.min(maxDelay, this.config.maxAnimationDelay));
            
            // Final positioning optimization
            this.optimizeNodePositions(nodes);
            
            // Finalize nodes (remove new-node class to prevent cleanup removal)
            this.finalizeNewNodes(nodes);

            // Add QuickBulk widgets to new nodes
            setTimeout(() => {
                this.addQuickBulkWidgetsToNewNodes(nodes);
            }, 500);
        }
        
        /**
         * Add nodes to the tree data structure
         * 
         * @param {Array} nodes New nodes to add
         */
        addNodesToTreeData(nodes) {
            // Add to global tree data if available
            if (window.slmmInterlinkingTreeData) {
                if (!window.slmmInterlinkingTreeData.nodes) {
                    window.slmmInterlinkingTreeData.nodes = [];
                }
                window.slmmInterlinkingTreeData.nodes.push(...nodes);
            }
            
            // Add to tree instance data
            if (this.tree && this.tree.data) {
                if (!this.tree.data.nodes) {
                    this.tree.data.nodes = [];
                }
                this.tree.data.nodes.push(...nodes);
            }
        }
        
        /**
         * Create visual elements for new nodes
         * 
         * @param {Array} nodes New nodes
         */
        createNodeElements(nodes) {
            // Create node groups
            const nodeGroups = this.svg.selectAll('.node')
                .data(nodes, d => d.id)
                .enter()
                .append('g')
                .attr('class', 'node new-node')
                .attr('data-page-id', d => d.page_id)
                .attr('data-node-id', d => d.id)
                .style('opacity', 0);
            
            // Add circles for nodes
            nodeGroups.append('circle')
                .attr('r', 0) // Start with 0 radius
                .attr('fill', d => d.color)
                .attr('stroke', '#ffffff')
                .attr('stroke-width', 2);
            
            // Add titles
            nodeGroups.append('text')
                .attr('dy', -15)
                .attr('text-anchor', 'middle')
                .style('font-size', '11px')
                .style('fill', '#f3f4f6')
                .style('font-weight', '500')
                .style('opacity', 0)
                .text(d => this.truncateTitle(d.title, 15));
            
            // Add success indicators that will animate
            nodeGroups.append('circle')
                .attr('class', 'success-ring')
                .attr('r', 0)
                .attr('fill', 'none')
                .attr('stroke', this.config.newNodeColor)
                .attr('stroke-width', 2)
                .attr('opacity', 0);
            
            // Store reference to node groups
            this.newNodeGroups = nodeGroups;
        }
        
        /**
         * Animate entrance of new nodes
         * 
         * @param {Array} nodes New nodes
         */
        animateNodeEntrance(nodes) {
            if (!this.newNodeGroups) return;
            
            nodes.forEach((node, index) => {
                setTimeout(() => {
                    this.animateSingleNodeEntrance(node);
                }, node.animationDelay);
            });
        }
        
        /**
         * Animate entrance of a single node
         * 
         * @param {Object} node Node data
         */
        animateSingleNodeEntrance(node) {
            const nodeGroup = this.svg.select(`[data-node-id="${node.id}"]`);
            
            if (nodeGroup.empty()) {
                if (window.SLMM && window.SLMM.debug) {
                    window.SLMM.debug.warn('QuickBulk D3', 'Node group not found for animation: ' + node.id);
                }
                return;
            }
            
            // Animate the group opacity
            nodeGroup
                .transition()
                .duration(this.config.animationDuration / 3)
                .style('opacity', 1);
            
            // Animate circle growth with bounce effect
            nodeGroup.select('circle')
                .transition()
                .duration(this.config.animationDuration)
                .ease(d3.easeElasticOut.amplitude(1).period(0.3))
                .attr('r', node.size)
                .attr('opacity', 0.9);
            
            // Animate title slide-in
            nodeGroup.select('text')
                .attr('transform', 'translate(0, 5)')
                .transition()
                .duration(this.config.animationDuration * 0.7)
                .delay(this.config.animationDuration * 0.3)
                .attr('transform', 'translate(0, 0)')
                .style('opacity', 1);
            
            // Animate success ring
            nodeGroup.select('.success-ring')
                .transition()
                .duration(1000)
                .delay(this.config.animationDuration * 0.5)
                .attr('r', node.size + 8)
                .attr('opacity', 1)
                .transition()
                .delay(1500)
                .duration(800)
                .attr('opacity', 0)
                .attr('r', node.size + 15)
                .on('end', function() {
                    d3.select(this).remove();
                });
        }
        
        /**
         * Highlight parent node to show it has new children
         * 
         * @param {Object} parentNode Parent node data
         */
        highlightParentNode(parentNode) {
            if (!parentNode) return;
            
            const parentElement = this.svg.select(`[data-node-id="${parentNode.id}"]`);
            
            if (parentElement.empty()) {
                if (window.SLMM && window.SLMM.debug) {
                    window.SLMM.debug.warn('QuickBulk D3', 'Parent element not found for highlighting');
                }
                return;
            }
            
            // Subtle glow effect
            parentElement.select('circle')
                .transition()
                .duration(300)
                .attr('stroke', this.config.parentHighlightColor)
                .attr('stroke-width', 3)
                .attr('r', this.config.parentNodePulseSize)
                .transition()
                .delay(2000)
                .duration(500)
                .attr('stroke', '#ffffff')
                .attr('stroke-width', 2)
                .attr('r', parentNode.size || this.config.nodeSize);
            
            // Update child count if there's an indicator
            this.updateParentChildCount(parentNode);
        }
        
        /**
         * Update parent-child relationship in data structure
         * 
         * @param {Object} parentNode Parent node
         * @param {Array} childNodes New child nodes
         */
        updateParentChildRelationship(parentNode, childNodes) {
            if (!parentNode.children) {
                parentNode.children = [];
            }
            
            childNodes.forEach(child => {
                parentNode.children.push(child.id);
                child.parent = parentNode.id;
            });
        }
        
        /**
         * Update parent child count indicator
         * 
         * @param {Object} parentNode Parent node
         */
        updateParentChildCount(parentNode) {
            const parentElement = this.svg.select(`[data-node-id="${parentNode.id}"]`);
            
            // Look for existing child count indicator
            let countElement = parentElement.select('.child-count');
            
            if (countElement.empty() && parentNode.children && parentNode.children.length > 0) {
                // Create child count indicator
                countElement = parentElement.append('text')
                    .attr('class', 'child-count')
                    .attr('x', 15)
                    .attr('y', -10)
                    .style('font-size', '10px')
                    .style('fill', '#10b981')
                    .style('font-weight', 'bold');
            }
            
            if (!countElement.empty() && parentNode.children) {
                countElement.text(parentNode.children.length);
            }
        }
        
        /**
         * Update D3.js simulation with new nodes
         * 
         * @param {Array} nodes New nodes
         */
        updateSimulation(nodes) {
            if (!this.simulation) {
                if (window.SLMM && window.SLMM.debug) {
                    window.SLMM.debug.warn('QuickBulk D3', 'Simulation not available for update');
                }
                // Clean up any artifacts since animation will fail
                this.cleanupArtifacts();
                return;
            }
            
            try {
                // Get current nodes from simulation
                let currentNodes = this.simulation.nodes() || [];
                
                // Add new nodes
                const allNodes = [...currentNodes, ...nodes];
                
                // Update simulation
                this.simulation
                    .nodes(allNodes)
                    .alpha(0.3) // Gentle restart
                    .restart();
                
                if (window.SLMM && window.SLMM.debug) {
                    window.SLMM.debug.log('QuickBulk D3', `Updated simulation with ${nodes.length} new nodes`);
                }
                
            } catch (error) {
                if (window.SLMM && window.SLMM.debug) {
                    window.SLMM.debug.warn('QuickBulk D3', 'Could not update simulation', error);
                }
                // Clean up artifacts on simulation error
                this.cleanupArtifacts();
            }
        }
        
        /**
         * Optimize positioning of new nodes
         * 
         * @param {Array} nodes New nodes
         */
        optimizeNodePositions(nodes) {
            // Simple collision detection and adjustment
            nodes.forEach((node, index) => {
                // Check for collisions with existing nodes
                const nodeElement = this.svg.select(`[data-node-id="${node.id}"]`);
                
                if (!nodeElement.empty()) {
                    // Apply slight random offset to prevent exact overlap
                    const randomX = (Math.random() - 0.5) * 20;
                    const randomY = (Math.random() - 0.5) * 20;
                    
                    nodeElement
                        .transition()
                        .duration(800)
                        .delay(index * 100)
                        .attr('transform', `translate(${node.x + randomX}, ${node.y + randomY})`);
                }
            });
        }
        
        /**
         * Add QuickBulk widgets to newly created nodes
         * 
         * @param {Array} nodes New nodes
         */
        addQuickBulkWidgetsToNewNodes(nodes) {
            if (!window.slmmQuickBulkController) {
                if (window.SLMM && window.SLMM.debug) {
                    window.SLMM.debug.warn('QuickBulk D3', 'QuickBulk controller not available');
                }
                return;
            }
            
            nodes.forEach(node => {
                const nodeElement = document.querySelector(`[data-node-id="${node.id}"]`);
                if (nodeElement) {
                    // Add QuickBulk widget to the new node
                    window.slmmQuickBulkController.enhanceCard(nodeElement);
                }
            });
        }
        
        /**
         * Truncate title for display
         * 
         * @param {string} title Full title
         * @param {number} maxLength Maximum length
         * @return {string} Truncated title
         */
        truncateTitle(title, maxLength) {
            if (!title) return '';
            return title.length > maxLength ? title.substring(0, maxLength - 3) + '...' : title;
        }
        
        /**
         * Sleep utility for async operations
         * 
         * @param {number} ms Milliseconds to sleep
         * @return {Promise} Promise that resolves after delay
         */
        sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        
        /**
         * Public API methods
         */
        
        /**
         * Manually add pages to tree (public API)
         * 
         * @param {Array} pages Array of page data
         * @param {string|Object} parentId Parent ID or parent node data
         */
        addPages(pages, parentId) {
            const parentNode = typeof parentId === 'string' ? 
                              this.findNodeById(parentId) : 
                              parentId;
            
            this.addCreatedPages(pages, { id: parentNode?.id || parentId });
        }
        
        /**
         * Find node by ID
         * 
         * @param {string} nodeId Node ID
         * @return {Object|null} Node data
         */
        findNodeById(nodeId) {
            let foundNode = null;
            
            this.svg.selectAll('.node').each(function(d) {
                if (d && (d.id === nodeId || d.page_id === nodeId)) {
                    foundNode = d;
                }
            });
            
            return foundNode;
        }
        
        /**
         * Get animation queue status
         * 
         * @return {Object} Queue status
         */
        getAnimationStatus() {
            return {
                isAnimating: this.isAnimating,
                queueLength: this.animationQueue.length,
                lastAnimation: this.lastAnimationTime || null
            };
        }
        
        /**
         * Clear animation queue (emergency stop)
         */
        clearAnimationQueue() {
            this.animationQueue = [];
            this.isAnimating = false;
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.log('QuickBulk D3', 'Animation queue cleared');
            }
        }
        
        /**
         * Finalize new nodes by transitioning them from "new-node" to permanent status
         *
         * @param {Array} nodes Array of nodes to finalize
         */
        finalizeNewNodes(nodes) {
            if (!nodes || nodes.length === 0) return;

            try {
                nodes.forEach(node => {
                    const nodeElement = this.svg.select(`[data-node-id="${node.id}"]`);
                    if (!nodeElement.empty()) {
                        // Remove the "new-node" class to make node permanent
                        // This prevents cleanup operations from removing successfully created nodes
                        const currentClass = nodeElement.attr('class') || '';
                        const newClass = currentClass.replace(/\bnew-node\b/g, '').replace(/\s+/g, ' ').trim();
                        nodeElement.attr('class', newClass);

                        if (window.SLMM && window.SLMM.debug) {
                            window.SLMM.debug.log('QuickBulk D3', `Finalized node: ${node.id} (${node.title})`);
                        }
                    }
                });

                if (window.SLMM && window.SLMM.debug) {
                    window.SLMM.debug.log('QuickBulk D3', `✅ Finalized ${nodes.length} nodes to permanent status`);
                }
            } catch (error) {
                if (window.SLMM && window.SLMM.debug) {
                    window.SLMM.debug.error('QuickBulk D3', 'Error finalizing nodes', error);
                }
            }
        }

        /**
         * Clean up floating QuickBulk artifacts
         */
        cleanupArtifacts() {
            try {
                const treeGroup = this.svg.select('g.tree-group, g');
                if (!treeGroup.empty()) {
                    const removedCount = treeGroup.selectAll('.node.new-node').size();
                    treeGroup.selectAll('.node.new-node').remove();
                    if (removedCount > 0) {
                        if (window.SLMM && window.SLMM.debug) {
                            window.SLMM.debug.log('QuickBulk D3', `🧹 Cleaned up ${removedCount} orphaned new-node artifacts (incomplete animations)`);
                        }
                    }
                }
            } catch (error) {
                if (window.SLMM && window.SLMM.debug) {
                    window.SLMM.debug.warn('QuickBulk D3', 'Error cleaning artifacts', error);
                }
            }
        }

        /**
         * MEMORY LEAK PROTECTION: Clear all tracked timeouts
         */
        clearTimeouts() {
            this.timeouts.forEach(function(timeoutId) {
                clearTimeout(timeoutId);
            });
            this.timeouts = [];

            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.log('QuickBulk D3', 'Cleared all timeouts');
            }
        }

        /**
         * MEMORY LEAK PROTECTION: Remove all tracked event listeners
         */
        clearEventListeners() {
            this.eventListeners.forEach(function(listener) {
                listener.element.removeEventListener(listener.event, listener.handler);
            });
            this.eventListeners = [];

            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.log('QuickBulk D3', 'Removed all event listeners');
            }
        }

        /**
         * MEMORY LEAK PROTECTION: Clean up global arrays
         */
        cleanupGlobalArrays() {
            // Clear tracked global data arrays to prevent memory accumulation
            this.globalDataArrays.forEach(function(arrayName) {
                if (window[arrayName] && window[arrayName].nodes) {
                    // Keep only recent nodes (last 50) to prevent unlimited growth
                    if (window[arrayName].nodes.length > 50) {
                        window[arrayName].nodes = window[arrayName].nodes.slice(-50);
                        if (window.SLMM && window.SLMM.debug) {
                            window.SLMM.debug.log('QuickBulk D3', `Trimmed global array ${arrayName} to 50 recent nodes`);
                        }
                    }
                }
            });

            this.globalDataArrays = [];
        }

        /**
         * MEMORY LEAK PROTECTION: Destroy instance and cleanup all resources
         */
        destroy() {
            if (this.isDestroyed) return;

            this.isDestroyed = true;

            // Clear all timeouts
            this.clearTimeouts();

            // Remove all event listeners
            this.clearEventListeners();

            // Clean up global arrays
            this.cleanupGlobalArrays();

            // Clear D3.js selections
            if (this.svg && !this.svg.empty()) {
                this.svg.selectAll('.new-node').remove();
            }

            // Clear stored references
            this.svg = null;
            this.simulation = null;
            this.tree = null;
            this.animationQueue = [];
            this.isAnimating = false;
            this.createdNodes.clear();
            this.config = null;

            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.log('QuickBulk D3', 'Instance destroyed and all resources cleaned up');
            }
        }

        /**
         * MEMORY LEAK PROTECTION: Get memory status for debugging
         */
        getMemoryStatus() {
            return {
                isDestroyed: this.isDestroyed,
                activeTimeouts: this.timeouts.length,
                activeEventListeners: this.eventListeners.length,
                trackedNodes: this.createdNodes.size,
                animationQueueLength: this.animationQueue.length,
                globalArraysTracked: this.globalDataArrays.length
            };
        }
    }
    
    /**
     * Auto-initialize when D3.js tree is ready
     */
    const initializeWhenReady = () => {
        // Check for D3.js tree instance
        const checkForTree = () => {
            if (window.slmmInterlinkingTree || document.querySelector('.slmm-tree-svg, .tree-svg')) {
                const treeInstance = window.slmmInterlinkingTree || null;
                window.slmmQuickBulkTreeIntegration = new SLMM_QuickBulk_TreeIntegration(treeInstance);
                if (window.SLMM && window.SLMM.debug) {
                    window.SLMM.debug.log('QuickBulk D3', 'Integration initialized successfully');
                }
            } else {
                setTimeout(checkForTree, 1000);
            }
        };
        
        checkForTree();
    };
    
    // Initialize based on document state
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeWhenReady);
    } else {
        initializeWhenReady();
    }

    // MEMORY LEAK PROTECTION: Automatic cleanup on page unload
    $(window).on('beforeunload.slmm-quickbulk', function() {
        if (window.slmmQuickBulkTreeIntegration) {
            window.slmmQuickBulkTreeIntegration.destroy();
            window.slmmQuickBulkTreeIntegration = null;
        }
    });

    // Global debug functions for console testing
    window.slmmQuickBulkMemoryStatus = function() {
        if (performance.memory) {
            const memory = performance.memory;
            console.log('SLMM QuickBulk D3 Memory Status:');
            console.log('Used:', Math.round(memory.usedJSHeapSize / 1048576) + 'MB');
            console.log('Total:', Math.round(memory.totalJSHeapSize / 1048576) + 'MB');
            console.log('Limit:', Math.round(memory.jsHeapSizeLimit / 1048576) + 'MB');

            if (window.slmmQuickBulkTreeIntegration) {
                const status = window.slmmQuickBulkTreeIntegration.getMemoryStatus();
                console.log('Component Status:', status);
            }
        }
    };

    window.slmmQuickBulkCleanup = function() {
        if (window.slmmQuickBulkTreeIntegration) {
            window.slmmQuickBulkTreeIntegration.destroy();
            console.log('SLMM QuickBulk D3: Manual cleanup completed');
        }
    };

})(jQuery, d3);