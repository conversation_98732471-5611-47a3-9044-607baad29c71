/**
 * j<PERSON><PERSON><PERSON> Lined Textarea Plugin
 *   http://alan.blog-city.com/jquerylinedtextarea.htm
 *
 * Copyright (c) 2010 <PERSON>
 *
 * Released under the MIT License:
 * http://www.opensource.org/licenses/mit-license.php
 * 
 * Usage:
 *   Displays a line number count column to the left of the textarea
 *   
 *   Class up your textarea with a given class, or target it directly
 *   with JQuery Selectors
 *   
 *   $(".lined").linedtextarea({
 *   	selectedLine: 10,
 *    selectedClass: 'lineselect'
 *   });
 *
 */

.linedwrap {
	border: 1px solid #c0c0c0;
	padding: 3px;
}

.linedtextarea {
	padding: 0px;
	margin: 0px;
}

.linedtextarea textarea, .linedwrap .codelines .lineno {
	font-size: 10pt;
	font-family: monospace;
	line-height: normal !important;
}

.linedtextarea textarea {
	padding-right:0.3em;
	padding-top:0.3em;
	border: 0;
}

.linedwrap .lines {
	margin-top: 0px;
	width: 50px;
	float: left;
	overflow: hidden;
	border-right: 1px solid #c0c0c0;
	margin-right: 10px;
}

.linedwrap .codelines {
	padding-top: 5px;
}

.linedwrap .codelines .lineno {
	color:#AAAAAA;
	padding-right: 0.5em;
	padding-top: 0.0em;
	text-align: right;
	white-space: nowrap;
}

.linedwrap .codelines .lineselect {
	color: red;
}