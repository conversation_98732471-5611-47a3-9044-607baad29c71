---
task: h-implement-smart-bulk-creation
branch: feature/smart-bulk-creation
status: pending
created: 2025-01-27
modules: [bulk-creation, interlinking, hierarchy-management]
---

# Smart Hierarchical Bulk Page Creation

## Problem/Goal
The current bulk page creation system already has sophisticated **post-type-to-parent** resolution (CPT permalink detection, archive page finding, etc.), but it lacks **user-provided hierarchical slug parsing**.

Currently, if a user provides these slugs:
- `diamond`
- `diamond/what-is-diamond`
- `diamond/alternatives`
- `diamond/alternatives/moissanite`

The system creates them **sequentially** without understanding that `diamond/alternatives/moissanite` should be a **child of `alternatives`**, which should be a **child of `diamond`**.

**We need to add slug-based hierarchy parsing that:**

1. **Parses forward-slash separated slugs** to detect parent-child relationships
2. **Creates pages in dependency order** (parents before children, regardless of input order)
3. **Handles slug sanitization** (leading/trailing slashes, URL encoding)
4. **Integrates with existing parent resolution system** (builds on top of current CPT logic)

## Success Criteria
- [ ] **Slug Hierarchy Parser**: Parse slash-separated slugs (`diamond/care/repair`) to detect parent-child relationships
- [ ] **Dependency-Ordered Creation**: Sort creation queue by hierarchy depth (Level 0, Level 1, Level 2...)
- [ ] **Slug Sanitization**: Handle `/diamond/care/repair` and `diamond/care/repair` identically
- [ ] **Dynamic Parent Lookup**: Find created parent page IDs during processing for `post_parent` assignment
- [ ] **Integration with Existing Logic**: Build on top of current `resolve_default_parent()` system
- [ ] **Error Recovery**: Handle cases where intermediate parents fail to create
- [ ] **Progress Visualization**: Show creation progress grouped by hierarchy levels

## Example Input/Output

**Input Titles:**
```
Diamond
What Is a Diamond?
What Are Diamond Alternatives?
Why Choose Moissanite Over Diamond?
What Makes Sapphires a Diamond Alternative?
```

**Input Slugs:**
```
diamond
diamond/what-is-diamond
diamond/alternatives
diamond/alternatives/moissanite
diamond/alternatives/sapphires
```

**Expected Creation Order:**
1. **Level 0**: `diamond` (parent page)
2. **Level 1**: `diamond/what-is-diamond`, `diamond/alternatives` (children of diamond)
3. **Level 2**: `diamond/alternatives/moissanite`, `diamond/alternatives/sapphires` (children of alternatives)

**WordPress Hierarchy Result:**
- Diamond (ID: 100, parent: 0)
  - What Is a Diamond? (ID: 101, parent: 100, slug: what-is-diamond)
  - What Are Diamond Alternatives? (ID: 102, parent: 100, slug: alternatives)
    - Why Choose Moissanite Over Diamond? (ID: 103, parent: 102, slug: moissanite)
    - What Makes Sapphires a Diamond Alternative? (ID: 104, parent: 102, slug: sapphires)

## Technical Requirements

**NEW: Slug Hierarchy Parser (add to `SLMM_QuickBulk_AJAX_Handler`):**
- Parse slash-separated slugs: `diamond/care/repair` → `['diamond', 'care', 'repair']`
- Calculate depth levels: `diamond` = 0, `diamond/care` = 1, `diamond/care/repair` = 2
- Build parent-child mapping: `repair` → `care` → `diamond`
- Sort creation order by depth (Level 0 first, then Level 1, etc.)

**NEW: Dynamic Parent Resolution (enhance existing `create_single_page()`):**
- **Existing**: Uses static `parent_id` from UI click
- **Enhanced**: Dynamically resolve parent from slug hierarchy during creation
- Maintain runtime mapping: `created_pages['diamond'] = 123` for child lookups
- Fall back to existing `resolve_default_parent()` for root-level pages

**NEW: Slug Sanitization (preprocessing before existing logic):**
- Strip leading/trailing slashes: `/diamond/care/` → `diamond/care`
- Handle URL encoding: `diamond%2Fcare` → `diamond/care`
- Validate segment compatibility with WordPress `sanitize_title()`

**EXISTING LOGIC TO PRESERVE:**
- ✅ `resolve_default_parent()` - CPT permalink structure detection
- ✅ `resolve_correct_parent()` - Post type validation and archive finding
- ✅ All security, rate limiting, and validation patterns

## Context Files
- @includes/bulk-creation/class-slmm-quickbulk-ajax-handler.php  # Current AJAX handler
- @includes/bulk-creation/class-slmm-bulk-page-creator.php       # Current page creator
- @assets/js/quickbulk-canvas-integration.js                     # Frontend integration

## User Notes
- **Build on existing system**: The current bulk creation already has sophisticated CPT parent resolution - don't break it!
- **New requirement**: Parse user-provided slash-separated slugs (`diamond/care/repair`) for hierarchy
- **UI unchanged**: Still uses left column titles, right column slugs - just smarter processing
- **Preserve features**: All existing bulk creation features (status, categories, post types) must work
- **Visual feedback**: Show creation progress grouped by hierarchy levels (Level 0, Level 1, etc.)
- **Example scenario**: User types `diamond`, `diamond/care`, `diamond/care/cleaning` → system creates diamond first, then care under diamond, then cleaning under care

## Context Manifest

### How This Currently Works: Existing Bulk Page Creation System

The current bulk page creation system in SLMM SEO Bundle operates through a sophisticated dual-component architecture that creates pages sequentially without understanding hierarchical relationships from URL slugs. The system consists of two primary classes working in tandem:

**SLMM_QuickBulk_AJAX_Handler** serves as the frontend gateway, handling all AJAX requests from the canvas-integrated UI. When a user initiates bulk creation (via `slmm_quickbulk_create_pages` action), the handler performs comprehensive security validation including nonce verification, capability checks (`edit_pages`), and SLMM plugin authorization. It extracts and sanitizes request data with intelligent post type resolution, handling cases where the target post type differs from the clicked parent's type. The handler implements sophisticated parent resolution logic - for example, when creating posts, it redirects to blog pages rather than individual CPT items, and for hierarchical CPTs, it maintains proper parent-child relationships. The system includes rate limiting (10 requests per 5-minute window) and comprehensive input validation (10KB limit, 100 pages per batch).

**SLMM_Bulk_Page_Creator** handles the actual WordPress integration, creating pages through optimized batch processing. It implements performance optimizations like suspending cache operations, increasing memory limits to 256MB, and disabling unnecessary WordPress hooks during bulk operations. The creator processes pages in chunks of 25 per batch with execution time monitoring (120 seconds max) to prevent timeouts. For each page, it generates unique slugs by appending numbers if conflicts exist, sets comprehensive meta fields (importance rating defaults to 3, SEO scores start at 45, batch tracking), and handles parent relationships through WordPress's `post_parent` field. The system integrates with the interlinking suite via `add_page_to_tree()` method and creates automatic backlinks to parent pages when `auto_link` is enabled.

**Frontend Integration** happens through `quickbulk-canvas-integration.js` which provides a modal interface with dual-column layout (titles on left, slugs on right). Users can input either titles or slugs, and the system intelligently detects input type - if input contains dashes but no spaces, it's treated as a slug and converted to title case, otherwise it's treated as a title and a slug is generated. The JavaScript handles real-time preview generation, AJAX communication with proper nonce management, and progress tracking with detailed success/error reporting.

**Current Architecture Limitations:** Pages are created strictly sequentially in the order provided, with no analysis of slug structure to determine hierarchical relationships. A user inputting `diamond`, `diamond/care`, `diamond/care/repair` would see all three pages created with their specified parent_id from the UI, but the system doesn't recognize that `diamond/care` should be a child of `diamond`, and `diamond/care/repair` should be a child of `diamond/care`. The current `post_parent` assignment is uniform across all pages in a batch, missing the opportunity to create proper hierarchical structures based on URL slug patterns.

### For Smart Hierarchical Implementation: What Needs to Connect

Since we're implementing intelligent hierarchical bulk creation that analyzes slug patterns, it will need to integrate with the existing system at several critical points while preserving all current functionality.

The current AJAX handler's `parse_page_titles()` method (lines 706-743) currently processes titles and slugs in sequential pairs without analyzing slug structure for hierarchy. Our enhancement will need to extend this method with a new slug analysis engine that can parse URL segments (`diamond/care/repair` → levels: diamond=0, care=1, repair=2) and build dependency maps showing parent-child relationships. The parser must handle slug sanitization consistently - treating `/diamond/care/repair` and `diamond/care/repair` identically by stripping leading/trailing slashes.

The bulk creator's `create_pages_batch()` method (lines 73-151) currently processes pages in the order provided. We'll need to implement level-based ordering where pages are grouped by hierarchy depth and processed level-by-level (all level 0 pages first, then level 1 pages that depend on level 0 parents, etc.). This requires maintaining a mapping of created page IDs so that when creating child pages, we can correctly set their `post_parent` to the ID of their hierarchical parent rather than the uniform parent_id from the original request.

The `create_single_page()` method (lines 233-369) needs modification to accept dynamic parent IDs resolved from the hierarchy analysis rather than using the static `$options['parent_id']`. When creating a page like `diamond/care/repair`, the system must look up the ID of the `diamond/care` page that was created in the previous level, not use the original clicked parent from the UI.

The frontend JavaScript in `quickbulk-canvas-integration.js` will need enhancement to show hierarchy visualization during creation progress. Instead of a simple "Creating page X of Y", users should see "Creating Level 0 pages", "Creating Level 1 pages", etc., with visual indicators showing the hierarchical structure being built.

**Integration Challenges:** The current system's optimization for batch processing (cache suspension, memory management) works well for sequential creation but needs adaptation for level-based processing. We'll need to maintain these optimizations while ensuring that parent pages are fully committed to the database before their children attempt to reference them. The interlinking suite integration also needs to handle hierarchical relationships properly - when a child page calls `add_page_to_tree()`, it should reference its hierarchical parent, not the original UI-selected parent.

**Error Handling Complexity:** Current error handling assumes independent page creation. Hierarchical creation introduces dependencies - if a parent page fails to create, all its children should either fail gracefully or be reassigned to a higher-level parent. We'll need sophisticated error recovery that can adjust the hierarchy dynamically when creation failures occur.

### Technical Reference Details

#### Component Interfaces & Signatures

**Current Parse Method (to be enhanced):**
```php
private function parse_page_titles($titles_text, $slugs_text = '')
// Returns: array of ['title' => string, 'slug' => string, 'line_number' => int]
```

**New Hierarchy Analysis Method (to be added):**
```php
private function analyze_slug_hierarchy($page_data)
// Returns: array with 'levels' => [0 => [pages], 1 => [pages]], 'dependencies' => [child_slug => parent_slug]
```

**Enhanced Creation Options:**
```php
// Current options structure extended with:
'hierarchy_mode' => boolean,
'level_order' => array, // [0, 1, 2] processing order
'parent_mapping' => array // [child_slug => parent_page_id] dynamic mapping
```

#### Data Structures

**Slug Analysis Structure:**
```php
array(
    'levels' => array(
        0 => array('diamond'), // Root level pages
        1 => array('diamond/care', 'diamond/alternatives'), // Level 1 children
        2 => array('diamond/care/repair', 'diamond/alternatives/moissanite') // Level 2 children
    ),
    'dependencies' => array(
        'diamond/care' => 'diamond',
        'diamond/care/repair' => 'diamond/care',
        'diamond/alternatives' => 'diamond',
        'diamond/alternatives/moissanite' => 'diamond/alternatives'
    ),
    'max_depth' => 2
)
```

**Progress Tracking Enhancement:**
```javascript
// Frontend progress structure
{
    total_levels: 3,
    current_level: 1,
    level_progress: {
        0: {completed: 1, total: 1, pages: ['diamond']},
        1: {completed: 0, total: 2, pages: ['diamond/care', 'diamond/alternatives']},
        2: {completed: 0, total: 2, pages: ['diamond/care/repair', 'diamond/alternatives/moissanite']}
    }
}
```

#### Configuration Requirements

**WordPress Capabilities:** System already handles `edit_pages` capability checks and SLMM authorization - no changes needed.

**Memory and Performance:** Current optimizations (256MB memory limit, cache suspension, 25 pages per batch) should be maintained. Level-based processing may need adjustment to batch limits - if level 1 has 50 pages, they should be processed in sub-batches.

**Database Operations:** Hierarchy creation requires immediate database commits between levels to ensure parent IDs are available for children. Current optimization of suspended cache operations may need selective restoration between levels.

#### File Locations

- **Primary Implementation:** `/includes/bulk-creation/class-slmm-quickbulk-ajax-handler.php` - extend `parse_page_titles()` method
- **Secondary Implementation:** `/includes/bulk-creation/class-slmm-bulk-page-creator.php` - modify `create_pages_batch()` for level-based processing
- **Frontend Enhancement:** `/assets/js/quickbulk-canvas-integration.js` - add hierarchy visualization to progress tracking
- **Integration Testing:** Existing AJAX endpoints require no URL changes, but request/response structures need hierarchy data
- **Documentation Updates:** `/includes/bulk-creation/` classes need comprehensive PHPDoc updates explaining hierarchy processing

## Work Log
- [2025-01-27] Task created - Smart hierarchical bulk page creation system